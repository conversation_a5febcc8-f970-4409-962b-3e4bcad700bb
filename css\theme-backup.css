/* Backup of the original theme.css file */
/* Created as part of the theme switching implementation */

/* Theme Variables - Using Bootstrap 5 CSS Variables */
:root {
    /* Creyos Core Brand Colors */
    --creyos-dark-blue: #3A4661; /* HEX: 3A4661, RGB: 58, 70, 97 */
    --creyos-accessible-teal: #0E7A95; /* HEX: 0E7A95, RGB: 14, 122, 149 */
    --creyos-teal: #4393A7; /* HEX: 4393A7, RGB: 67, 147, 167 */
    --creyos-icon-blue: #12C0DB; /* HEX: 12C0DB, RGB: 18, 192, 219 */

    /* Creyos Brand Highlight Colors */
    --creyos-accent-blue: #BDF2FF; /* HEX: BDF2FF, RGB: 189, 242, 255 */
    --creyos-accent-blue-80: #CAF5FF; /* HEX: CAF5FF, RGB: 202, 245, 255 */
    --creyos-accent-blue-60: #D7F7FF; /* HEX: D7F7FF, RGB: 215, 247, 255 */
    --creyos-accent-blue-40: #E5FAFF; /* HEX: E5FAFF, RGB: 229, 250, 255 */

    /* Override Bootstrap Variables with Creyos Theme */
    --bs-primary: var(--creyos-icon-blue);
    --bs-secondary: var(--creyos-teal);
    --bs-success: var(--creyos-accessible-teal);
    --bs-info: var(--creyos-accent-blue);
    --bs-warning: var(--creyos-teal);
    --bs-danger: var(--creyos-dark-blue);
    --bs-light: var(--creyos-accent-blue-40);
    --bs-dark: var(--creyos-dark-blue);

    /* Background and Text Colors */
    --bs-body-bg: #ffffff;
    --bs-body-bg-rgb: 255, 255, 255;
    --bs-body-color: var(--creyos-dark-blue);
    --bs-body-color-rgb: 58, 70, 97;

    /* Font Family */
    --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    
    /* Navbar Background */
    --navbar-bg: var(--creyos-dark-blue);
}
