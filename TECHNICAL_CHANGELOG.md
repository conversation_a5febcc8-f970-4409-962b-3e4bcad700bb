# CSV Analytics Dashboard Technical Changelog

This document provides detailed technical information about changes made to the CSV Analytics Dashboard, intended for developers and maintainers.

## [v1.9.4] - 2024-04-09 (Build 55)

### Creyos Brand Color Implementation
- Created a centralized color management system:
  - Added `creyosColors` object in app.js with all Creyos brand colors
  - Added `getCreyosChartColors()` function to get an array of Creyos colors
  - Added `hexToRgb()` function to convert hex colors to RGB format for transparency
- Updated all icon colors in HTML:
  - Created new CSS classes for Creyos brand colors (text-creyos-icon-blue, text-creyos-teal, etc.)
  - Updated all icons in the HTML to use these new classes
  - Replaced all Bootstrap color classes with Creyos-specific classes
- Updated all chart colors in JavaScript:
  - Updated all chart colors to use the Creyos brand colors
  - Used the `creyosColors` object for consistent color references
  - Used the `getCreyosChartColors()` function for pie and doughnut charts
  - Used the `hexToRgb()` function for colors with transparency
- Updated PDF generation:
  - Updated icon colors in PDF generation to use Creyos brand colors
  - Updated chart colors in PDF generation to use Creyos brand colors

### Technical Implementation Details
- Added CSS classes for all Creyos brand colors
- Updated all Bootstrap overrides to use only Creyos brand colors
- Added helper functions for color management
- Fixed version display to show correct version
- Removed sample page to simplify the application

### File Changes
- Modified: `css/theme.css` (updated color variables and added new color classes)
- Modified: `js/app.js` (added color management system and updated chart colors)
- Modified: `js/charts.js` (updated chart defaults to use Creyos colors)
- Modified: `index.html`, `how-to-use.html` (updated icon classes and removed sample page references)
- Modified: `js/version.js` (updated version information)
- Removed: `sample.html` (removed sample page from the application)

## [v1.9.3] - 2024-04-09 (Build 54)

### Timezone Filter Removal
- Removed timezone dropdown from filter section:
  - Removed the timezone dropdown from the filter UI in analytics-filters.js
  - Removed timezone-related code from filter application and reset functions
- Simplified time-based analysis functions:
  - Removed timezone conversion functions from app.js
  - Simplified `formatHour()` function to only format hours without timezone information
  - Updated all time-based analysis functions to use UTC time only
  - Set all chart titles to indicate UTC time
- Updated documentation:
  - Removed timezone-related information from the How to Use page
  - Updated filter section to reflect the simplified filter options

### Technical Implementation Details
- Removed `convertToTimezone()` function and related timezone conversion code
- Removed `getTimezoneDisplayString()` function
- Simplified `formatHourWithTimezone()` to `formatHour()`
- Removed global `selectedTimezone` variable
- Ensured all time-based charts use UTC time consistently

### File Changes
- Modified: `js/app.js` (removed timezone-related functions and simplified time handling)
- Modified: `js/analytics-filters.js` (removed timezone dropdown and related code)
- Modified: `how-to-use.html` (updated filter section documentation)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.9.2] - 2024-04-09 (Build 53)

### Chart Label Fixes
- Fixed issue with chart labels not updating when timezone filter is changed:
  - Simplified `formatHourWithTimezone()` function to remove redundant timezone information from labels
  - Updated timezone information to only appear in chart titles for cleaner display
  - Modified `processFilteredData()` function to always call time-based analysis functions
  - Removed conditional checks for time-based functions to ensure they're always executed

### Technical Implementation Details
- Updated `updateChartLabels()` function to properly handle timezone changes
- Added checks to ensure chart data is properly refreshed when filters are applied
- Improved error handling in chart rendering functions
- Added defensive programming to handle potential undefined variables

### File Changes
- Modified: `js/app.js` (fixed chart label updating and improved error handling)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `how-to-use.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.9.1] - 2024-04-09 (Build 52)

### Default Selection Changes
- Changed all "Select Insights for Report" options to be deselected by default:
  - Updated checkbox initialization in app.js
  - Modified PDF generation to handle cases where no insights are selected
  - Added warning message when generating PDF with no insights selected

### Timezone Display Improvements
- Enhanced timezone display in chart titles:
  - Added `getTimezoneDisplayString()` function to format timezone information
  - Updated chart titles to include timezone information in a more readable format
  - Improved timezone dropdown to show more user-friendly timezone names

### PDF Generation Fixes
- Fixed PDF generation error related to undefined labels:
  - Added null checks for all chart data before generating PDF
  - Improved error handling in PDF generation process
  - Added fallback for missing chart data

### Technical Implementation Details
- Added defensive programming to handle potential undefined variables
- Improved error handling in chart rendering and PDF generation
- Enhanced timezone handling with better formatting and display

### File Changes
- Modified: `js/app.js` (fixed PDF generation and improved timezone display)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `how-to-use.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.9.0] - 2024-04-09 (Build 51)

### Timezone Selection Feature
- Added timezone selection for time-based charts:
  - Implemented timezone dropdown in the filter section
  - Added timezone conversion functions for time-based data
  - Updated chart titles to include timezone information
- Enhanced time-based analysis:
  - Added proper timezone handling for all time-based charts
  - Improved hour formatting with timezone information
  - Updated chart labels to reflect selected timezone

### Technical Implementation Details
- Added `convertToTimezone()` function for timezone conversion
- Implemented `formatHourWithTimezone()` function for consistent hour formatting
- Added global `selectedTimezone` variable to track user selection
- Updated all time-based analysis functions to use the selected timezone
- Enhanced filter handling to include timezone selection

### File Changes
- Modified: `js/app.js` (added timezone selection and conversion functions)
- Modified: `js/analytics-filters.js` (added timezone dropdown)
- Modified: `how-to-use.html` (added timezone selection documentation)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.8.3] - 2024-04-09 (Build 50)

### Variable Scope Fix
- Fixed variable scope issue in PDF generation:
  - Resolved issue with variables being out of scope during PDF generation
  - Added proper variable declarations to ensure correct scope
  - Improved error handling in PDF generation process

### Technical Implementation Details
- Moved variable declarations to appropriate scope
- Added error handling for PDF generation process
- Improved logging for debugging purposes

### File Changes
- Modified: `js/app.js` (fixed variable scope issues)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `how-to-use.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.8.2] - 2024-04-09 (Build 49)

### PDF Chart Data Fix
- Fixed issue with chart data not being properly included in PDF:
  - Added checks to ensure chart data is available before PDF generation
  - Improved error handling for missing chart data
  - Added fallback for charts with no data

### How to Use Page Enhancement
- Added comprehensive How to Use page:
  - Created detailed documentation for all features
  - Added step-by-step instructions for common tasks
  - Included troubleshooting section for common issues
  - Added examples and screenshots for better understanding

### Technical Implementation Details
- Improved chart data handling in PDF generation
- Enhanced error handling for PDF generation process
- Created comprehensive documentation with detailed examples

### File Changes
- Modified: `js/app.js` (fixed PDF chart data issue)
- Added/Modified: `how-to-use.html` (comprehensive documentation)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.8.1] - 2024-04-09 (Build 48)

### Bug Fixes
- Fixed duplicate info toggles:
  - Removed duplicate information toggle buttons
  - Improved toggle functionality with better event handling
  - Added checks to prevent duplicate event listeners
- Fixed PDF generation error:
  - Resolved issue with PDF generation failing due to missing elements
  - Added null checks for all elements before PDF generation
  - Improved error handling in PDF generation process

### jQuery Dependency Removal
- Removed jQuery dependency:
  - Replaced all jQuery functions with vanilla JavaScript
  - Updated event handling to use addEventListener
  - Improved DOM manipulation with modern JavaScript methods
  - Enhanced selector usage with querySelector and querySelectorAll

### Technical Implementation Details
- Replaced jQuery functions with vanilla JavaScript equivalents
- Improved event handling with addEventListener
- Enhanced DOM manipulation with modern JavaScript methods
- Added null checks and improved error handling

### File Changes
- Modified: `js/app.js` (removed jQuery dependency and fixed bugs)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (removed jQuery references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.8.0] - 2024-04-09 (Build 47)

### PDF Report Redesign
- Redesigned PDF report with new layout:
  - Implemented two-column chart layout for better space utilization
  - Added improved styling with Creyos brand colors
  - Enhanced header and footer with better formatting
  - Added page numbers and build date
- Improved chart rendering in PDF:
  - Optimized chart sizes for better readability
  - Enhanced chart legends with better formatting
  - Improved chart titles with more descriptive text
  - Added proper spacing between charts

### Technical Implementation Details
- Implemented two-column layout for charts in PDF
- Enhanced PDF styling with Creyos brand colors
- Improved chart rendering with optimized sizes
- Added build date and page numbers to PDF
- Enhanced header and footer formatting

### File Changes
- Modified: `js/app.js` (redesigned PDF report generation)
- Modified: `css/pdf-styles.css` (updated PDF styling)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.7.4] - 2024-04-09 (Build 46)

### Protocol Display Improvements
- Improved protocol display with shortened names and tooltips:
  - Implemented ellipsis for long protocol names
  - Added tooltips to show full protocol names on hover
  - Ensured full names are used in PDF reports
  - Updated protocol name handling in charts and tables

### Technical Implementation Details
- Added function to shorten protocol names with ellipsis
- Implemented tooltips for showing full names on hover
- Updated chart and table rendering to use shortened names
- Ensured PDF reports use full protocol names

### File Changes
- Modified: `js/app.js` (improved protocol name handling)
- Modified: `css/styles.css` (added tooltip styling)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.7.3] - 2024-04-09 (Build 45)

### Time-Based Analysis
- Implemented Time-Based Analysis charts:
  - Added Time of Day Analysis chart
  - Added Day of Week Analysis chart
  - Added Weekly Trends Analysis chart
  - Enhanced data processing for time-based analysis

### Technical Implementation Details
- Added functions for time-based data processing
- Implemented chart rendering for time-based analysis
- Enhanced data filtering for time-based charts
- Added proper formatting for time and date display

### File Changes
- Modified: `js/app.js` (added time-based analysis functions)
- Modified: `js/charts.js` (added time-based chart rendering)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.7.2] - 2024-04-09 (Build 44)

### jQuery Dependency Fix
- Fixed jQuery dependency for collapsible information boxes:
  - Added proper jQuery initialization for collapsible boxes
  - Improved event handling for toggle buttons
  - Enhanced animation for smooth transitions
  - Added proper error handling for jQuery functions

### Technical Implementation Details
- Added proper jQuery initialization for collapsible boxes
- Improved event handling with better selectors
- Enhanced animation with smooth transitions
- Added error handling for jQuery functions

### File Changes
- Modified: `js/app.js` (fixed jQuery dependency)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.7.1] - 2024-04-09 (Build 43)

### Collapsible Information Boxes
- Implemented collapsible information boxes:
  - Added toggle buttons for each information section
  - Implemented smooth animation for collapsing/expanding
  - Added proper styling for toggle buttons
  - Enhanced user experience with interactive elements

### Technical Implementation Details
- Added toggle functionality for information boxes
- Implemented smooth animation with CSS transitions
- Enhanced styling with proper icons and colors
- Improved user experience with interactive elements

### File Changes
- Modified: `js/app.js` (added collapsible box functionality)
- Modified: `css/styles.css` (added styling for collapsible boxes)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.7.0] - 2024-04-09 (Build 42)

### Protocol Type Removal
- Removed protocol type from filter data:
  - Simplified filter options by removing protocol type
  - Updated filter UI to reflect the change
  - Modified filter application logic
  - Updated documentation to reflect the change

### Technical Implementation Details
- Removed protocol type from filter data structure
- Updated filter UI to remove protocol type option
- Modified filter application logic to handle the change
- Updated documentation to reflect the simplified filters

### File Changes
- Modified: `js/app.js` (removed protocol type from filters)
- Modified: `js/analytics-filters.js` (updated filter UI)
- Modified: `how-to-use.html` (updated filter documentation)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.6.0] - 2024-04-09 (Build 41)

### Advanced Analytics Removal
- Removed Advanced Analytics feature:
  - Removed Advanced Analytics tab from the UI
  - Deleted related JavaScript files
  - Updated documentation to reflect the change
  - Simplified the application structure

### Technical Implementation Details
- Removed Advanced Analytics tab from the UI
- Deleted advanced-analytics.js and related files
- Updated documentation to reflect the removal
- Simplified the application structure and navigation

### File Changes
- Removed: `js/advanced-analytics.js` and related files
- Modified: `js/app.js` (removed Advanced Analytics references)
- Modified: `index.html` (removed Advanced Analytics tab)
- Modified: `how-to-use.html` (updated documentation)
- Modified: `js/version.js` (updated version information)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.5.0] - 2024-04-09 (Build 40)

### PDF Report Formatting
- Enhanced PDF report formatting:
  - Reduced Creyos logo size by 20% on the first page
  - Added padding for logos on subsequent pages
  - Improved layout with better spacing and alignment
  - Enhanced chart rendering with optimized sizes

### Technical Implementation Details
- Modified logo sizing in PDF generation
- Added padding for logos on subsequent pages
- Improved layout with better spacing and alignment
- Enhanced chart rendering with optimized sizes

### File Changes
- Modified: `js/app.js` (improved PDF formatting)
- Modified: `css/pdf-styles.css` (updated PDF styling)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.4.0] - 2024-04-09 (Build 39)

### Assessment Logic Update
- Updated Assessment Completion Rate calculation:
  - Modified calculation to count as 1 on ratios like 1/1, 2/1, 3/1, 4/1, 3/2, 4/3, 5/4, etc.
  - Updated documentation to reflect the change
  - Enhanced data processing for better accuracy
  - Improved chart rendering with updated data

### Technical Implementation Details
- Modified Assessment Completion Rate calculation
- Updated documentation to reflect the change
- Enhanced data processing for better accuracy
- Improved chart rendering with updated data

### File Changes
- Modified: `js/app.js` (updated Assessment Completion Rate calculation)
- Modified: `how-to-use.html` (updated documentation)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.3.0] - 2024-04-09 (Build 38)

### Protocol Counting Logic Update
- Updated protocol counting logic:
  - Modified to omit timestamp numbers at the beginning of protocol names
  - Updated to use full protocol names without timestamps
  - Enhanced protocol categorization logic
  - Improved data processing for better accuracy

### Technical Implementation Details
- Modified protocol counting logic to handle timestamps
- Updated protocol categorization for ADHD and MCI protocols
- Enhanced data processing for better accuracy
- Improved chart rendering with updated data

### File Changes
- Modified: `js/app.js` (updated protocol counting logic)
- Modified: `how-to-use.html` (updated documentation)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.2.0] - 2024-04-09 (Build 37)

### UI Improvements
- Enhanced user interface:
  - Implemented cleaner UI with options in a collapsible side menu
  - Replaced large images with icons for better space utilization
  - Improved alignment and spacing for better readability
  - Enhanced responsive design for mobile devices

### Technical Implementation Details
- Improved UI layout with better spacing and alignment
- Replaced large images with icons
- Enhanced responsive design for mobile devices
- Improved user experience with better navigation

### File Changes
- Modified: `js/app.js` (improved UI functionality)
- Modified: `css/styles.css` (updated UI styling)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.1.0] - 2024-04-09 (Build 36)

### CSV to JSON Conversion
- Implemented CSV to JSON conversion:
  - Added functionality to convert CSV data to JSON before processing
  - Enhanced data handling with better type conversion
  - Improved error handling for CSV parsing
  - Added support for different CSV formats

### Technical Implementation Details
- Added CSV to JSON conversion functionality
- Enhanced data handling with better type conversion
- Improved error handling for CSV parsing
- Added support for different CSV formats

### File Changes
- Modified: `js/app.js` (added CSV to JSON conversion)
- Modified: `js/version.js` (updated version information)
- Modified: `index.html`, `sample.html`, `clear-cache.html` (updated version references)
- Modified: `CHANGELOG.md` and `TECHNICAL_CHANGELOG.md` (documentation updates)

## [v1.0.0] - 2024-04-09 (Build 35)

### Initial Release
- First public release of the CSV Analytics Dashboard:
  - Implemented basic CSV parsing and data visualization
  - Added chart generation for key metrics
  - Implemented PDF report generation
  - Added filter functionality for data analysis

### Technical Implementation Details
- Implemented CSV parsing with PapaParse
- Added chart generation with Chart.js
- Implemented PDF report generation with jsPDF and html2canvas
- Added filter functionality for data analysis

### File Changes
- Initial release of all files
