/* Main Styles - Bootstrap 5 Blue Theme */
body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1.25rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--bs-box-shadow);
}

.card-header {
    font-weight: 600;
}

/* Drag and Drop Upload Styles */
.drag-drop-area {
    border: 2px dashed var(--creyos-accent-blue);
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background-color: rgba(189, 242, 255, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.drag-drop-area:hover {
    background-color: rgba(189, 242, 255, 0.2);
    border-color: var(--creyos-icon-blue);
}

.drag-drop-area.drag-active {
    background-color: rgba(189, 242, 255, 0.3);
    border-color: var(--creyos-icon-blue);
    transform: scale(1.02);
}

/* Style for when a file is selected */
.drag-drop-area .file-info {
    cursor: default;
    transition: all 0.3s ease;
}

/* Make the remove button more visible on hover */
.drag-drop-area .file-info:hover #removeFile {
    opacity: 1;
}

#removeFile {
    opacity: 0.7;
    transition: all 0.2s ease;
}

#removeFile:hover {
    background-color: #dc3545;
    color: white;
}

.drag-drop-message {
    width: 100%;
}

.drag-drop-message p {
    margin-bottom: 0.5rem;
    color: var(--creyos-dark-blue);
}

.file-info {
    width: 100%;
    padding: 1rem;
    background-color: rgba(189, 242, 255, 0.2);
    border-radius: 0.5rem;
}

/* Report Preview Styles */
.report-preview {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.report-section {
    margin-bottom: 2rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid var(--bs-border-color);
}

.report-section h4 {
    margin-bottom: 1.25rem;
    font-weight: 600;
    color: var(--bs-body-color);
}

/* Chart Containers */
[id$="Chart"] {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

[id$="Chart"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
}

/* Bootstrap 5 Text Utilities - Using Bootstrap Variables */
/* These are now handled by theme.css with Bootstrap variables */

/* PDF Export Styles */
@media print {
    body {
        background-color: white;
    }

    .report-preview {
        box-shadow: none;
        padding: 0;
    }

    .navbar, .card-header, footer, button, .form-check {
        display: none !important;
    }

    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }

    .card {
        border: none;
        box-shadow: none;
    }

    .card-body {
        padding: 0;
    }
}

/* Loading Spinner */
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 12.5rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    color: var(--bs-primary);
}

/* Version Display */
.version-display {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease-in-out;
}

.version-display:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .report-preview {
        padding: 1rem;
    }

    [id$="Chart"] {
        height: 250px !important;
    }

    .version-display {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}


