/**
 * CSV Analytics Dashboard Version Information
 *
 * This file contains the version information for the CSV Analytics Dashboard.
 * Update this file whenever changes are made to the application.
 *
 * Version History:
 * v1.0.0 (build 1) - 2024-04-08: Initial release
 * v1.0.1 (build 2) - 2024-04-08: Added version display
 * v1.0.2 (build 3) - 2024-04-08: Fixed chart initialization issues
 * v1.1.0 (build 5) - 2024-04-08: Complete rewrite of chart system
 * v1.1.1 (build 6) - 2024-04-08: Added cache busting
 * v1.1.2 (build 7) - 2024-04-08: Fixed PDF generation
 * v1.2.0 (build 8) - 2024-04-08: Added theme system
 * v1.3.0 (build 9) - 2024-04-08: Improved UI with modern color palette
 * v1.3.1 (build 10) - 2024-04-08: Fixed CSP issues and chart rendering
 * v1.3.2 (build 11) - 2024-04-08: Added data labels to PDF charts
 * v1.3.3 (build 12) - 2024-04-08: Enhanced PDF chart labels and removed version header
 * v1.3.4 (build 13) - 2024-04-08: Fixed PDF generation with visible chart values
 * v1.3.5 (build 14) - 2024-04-08: Fixed chart display in PDFs and improved margins
 * v1.3.6 (build 15) - 2024-04-08: Fixed PDF generation with manual chart labels
 * v1.3.7 (build 16) - 2024-04-08: Added Creyos logo to PDF headers and improved pie chart labels
 * v1.3.8 (build 17) - 2024-04-08: Fixed PDF filename generation error
 * v1.3.9 (build 18) - 2024-04-08: Improved CSV parsing with support for quoted fields and UTF-8 encoding
 * v1.4.0 (build 19) - 2024-04-08: Reorganized file structure and removed unused files
 * v1.4.1 (build 20) - 2024-04-08: Improved How to Use page with modern card-based layout and Bootstrap icons
 * v1.4.2 (build 21) - 2024-04-08: Fixed version tooltip to display current date instead of static date
 * v1.5.0 (build 22) - 2024-04-08: Added CSV to JSON conversion for improved data processing
 * v1.5.1 (build 23) - 2024-04-08: Updated UI with Proxima Nova font and Creyos color palette
 * v1.5.2 (build 24) - 2024-04-08: Added data preview modal and fixed PDF logo aspect ratio
 * v1.5.3 (build 25) - 2024-04-08: Fixed CSP issues, data preview modal, and PDF generation
 * v1.5.4 (build 26) - 2024-04-08: Improved protocol categorization with better grouping
 * v1.5.5 (build 27) - 2024-04-08: Refined protocol counting to handle ADHD as special case
 * v1.5.6 (build 28) - 2024-04-08: Added MCI detection based on Creyos Dementia Assessment tasks
 * v1.5.7 (build 29) - 2024-04-08: Improved protocol handling for entries without timestamps
 * v1.5.8 (build 30) - 2024-04-08: Enhanced protocol detection with completion ratio analysis
 * v1.5.9 (build 31) - 2024-04-08: Updated Assessment Completion Rate calculation
 * v1.6.0 (build 32) - 2024-04-08: Added advanced analytics features (patient engagement, time analysis, protocol analysis)
 * v1.6.0 (build 33) - 2024-04-08: Added additional analytics features (filters, protocol comparison, demographics, advanced dashboard)
 * v1.6.1 (build 34) - 2024-04-08: Fixed chart type issues and removed unused font references
 * v1.6.1 (build 35) - 2024-04-08: Fixed protocol heatmap chart creation
 * v1.6.1 (build 36) - 2024-04-09: Fixed protocol comparison chart and regex patterns
 * v1.6.2 (build 37) - 2024-04-09: Implemented direct Chart.js approach for all advanced analytics
 * v1.6.3 (build 38) - 2024-04-09: Optimized codebase by removing unused files and functions
 * v1.6.3 (build 39) - 2024-04-09: Updated footer to display "Creyos CS 2025"
 * v1.6.4 (build 40) - 2024-04-09: Simplified protocol name handling and removed date from navigation
 * v1.6.5 (build 41) - 2024-04-09: Updated charts to use real data and made filters collapsible
 * v1.7.0 (build 42) - 2024-04-09: Removed Advanced Analytics features
 * v1.7.1 (build 43) - 2024-04-09: Added Time-Based Analysis, collapsible information boxes, and improved PDF report layout
 * v1.7.2 (build 44) - 2024-04-09: Fixed jQuery dependency for collapsible information boxes
 * v1.7.3 (build 45) - 2024-04-09: Implemented Time-Based Analysis charts functionality
 * v1.7.4 (build 46) - 2024-04-09: Improved protocol display with shortened names and tooltips
 * v1.8.0 (build 47) - 2024-04-09: Redesigned PDF report with new layout, two-column charts, and improved styling
 * v1.8.1 (build 48) - 2024-04-09: Fixed duplicate info toggles, PDF generation error, and removed jQuery dependency
 * v1.8.2 (build 49) - 2024-04-09: Fixed PDF chart data error and added comprehensive How to Use page
 * v1.8.3 (build 50) - 2024-04-09: Fixed variable scope issue in PDF generation
 * v1.9.0 (build 51) - 2024-04-09: Added timezone selection for time-based charts
 * v1.9.1 (build 52) - 2024-04-09: Fixed PDF generation error, improved timezone display, and deselected all insights by default
 * v1.9.2 (build 53) - 2024-04-09: Fixed chart labels not updating when timezone filter is changed
 * v1.9.3 (build 54) - 2024-04-09: Removed timezone filter as per user request
 * v1.9.4 (build 55) - 2024-04-09: Updated all icons and charts to use only Creyos brand colors
 */

// Version information
const VERSION = {
    major: 1,      // Major version (significant changes)
    minor: 9,      // Minor version (feature additions)
    patch: 4,      // Patch version (bug fixes)
    build: 55,      // Build number (incremented with each change)
    date: '2024-04-09',  // Current date

    // Return the full version string
    toString: function() {
        return `v${this.major}.${this.minor}.${this.patch} (build ${this.build})`;
    },

    // Return a short version string
    toShortString: function() {
        return `v${this.major}.${this.minor}.${this.patch}`;
    },

    // Update the version (for development use)
    update: function(type = 'patch') {
        switch(type.toLowerCase()) {
            case 'major':
                this.major++;
                this.minor = 0;
                this.patch = 0;
                break;
            case 'minor':
                this.minor++;
                this.patch = 0;
                break;
            case 'patch':
            default:
                this.patch++;
                break;
        }

        this.build++;
        this.date = new Date().toISOString().split('T')[0];

        // Update version displays
        this.updateDisplays();

        console.log(`Version updated to ${this.toString()}`);
        return this.toString();
    },

    // Update all version displays in the DOM
    updateDisplays: function() {
        console.log('Version display updated to:', this.toShortString(), '(build ' + this.build + ')');
        console.log('Current date:', this.date);

        const versionElements = document.querySelectorAll('.version-display');
        const currentDate = new Date().toISOString().split('T')[0];
        versionElements.forEach(element => {
            element.textContent = this.toShortString();
            element.setAttribute('title', `Build ${this.build} - ${currentDate}`);
        });
    }
};

// Display the version in the console
console.log(`CSV Analytics Dashboard ${VERSION.toString()}`);

// Force version update immediately
VERSION.updateDisplays();

// Update the version display in the DOM when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Force version update immediately
    VERSION.updateDisplays();

    // Log version information
    console.log('Version display updated to:', VERSION.toString());
    console.log('Current date:', VERSION.date);
});

// Also update version when the window loads (as a backup)
window.addEventListener('load', function() {
    VERSION.updateDisplays();
});
