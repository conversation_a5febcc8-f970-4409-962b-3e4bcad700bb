/* CSV Analytics Dashboard Core Functionality Export */
/* For implementation in a content frame within another application */

/* SEGMENT 1: HTML STRUCTURE */
/* This segment contains the minimal HTML structure needed for the dashboard */

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Analytics Dashboard</title>
</head>
<body>
    <!-- Upload Section -->
    <div id="uploadSection">
        <h5>Upload Aggregate Report CSV</h5>
        <form id="uploadFormMain">
            <label for="csvFileMain">Select CSV File</label>
            <input type="file" id="csvFileMain" accept=".csv">
            <button type="button" id="uploadBtnMain">Upload & Analyze</button>
        </form>
    </div>

    <!-- Filter Section -->
    <div id="filterSection" style="display: none;">
        <h5>Filter Options</h5>
        <div>
            <label for="completionFilter">Completion Status:</label>
            <select id="completionFilter">
                <option value="all" selected>All Assessments</option>
                <option value="completed">Completed Only</option>
                <option value="incomplete">Incomplete Only</option>
            </select>
        </div>
        <div>
            <label for="dateRangeFilter">Date Range:</label>
            <select id="dateRangeFilter">
                <option value="all" selected>All Time</option>
                <option value="30">Last 30 Days</option>
                <option value="90">Last 90 Days</option>
                <option value="180">Last 180 Days</option>
                <option value="365">Last Year</option>
            </select>
        </div>
        <button id="applyFiltersBtn">Apply Filters</button>
        <button id="resetFiltersBtn">Reset Filters</button>
    </div>

    <!-- Report Options Section -->
    <div id="reportOptionsSection" style="display: none;">
        <h5>Select Insights for Report</h5>
        <button id="selectAllBtn">Select All</button>
        <button id="deselectAllBtn">Deselect All</button>
        <div id="insightOptions">
            <div>
                <input type="checkbox" id="uniquePatientsOption" class="insight-option">
                <label for="uniquePatientsOption">Unique Patients Count</label>
            </div>
            <div>
                <input type="checkbox" id="assessmentMethodsOption" class="insight-option">
                <label for="assessmentMethodsOption">Assessment Methods Distribution</label>
            </div>
            <div>
                <input type="checkbox" id="completionTimeOption" class="insight-option">
                <label for="completionTimeOption">Average Completion Time</label>
            </div>
            <div>
                <input type="checkbox" id="protocolsOption" class="insight-option">
                <label for="protocolsOption">Most Used Protocols</label>
            </div>
            <div>
                <input type="checkbox" id="practitionerOption" class="insight-option">
                <label for="practitionerOption">Practitioner Activity</label>
            </div>
            <div>
                <input type="checkbox" id="ageDistributionOption" class="insight-option">
                <label for="ageDistributionOption">Patient Age Distribution</label>
            </div>
            <div>
                <input type="checkbox" id="completionRateOption" class="insight-option">
                <label for="completionRateOption">Assessment Completion Rate</label>
            </div>
            <div>
                <input type="checkbox" id="monthlyTrendsOption" class="insight-option">
                <label for="monthlyTrendsOption">Monthly Assessment Trends</label>
            </div>
            <div>
                <input type="checkbox" id="scoreDistributionOption" class="insight-option">
                <label for="scoreDistributionOption">Score Distribution</label>
            </div>
            <div>
                <input type="checkbox" id="timeOfDayOption" class="insight-option">
                <label for="timeOfDayOption">Time of Day Distribution</label>
            </div>
            <div>
                <input type="checkbox" id="dayOfWeekOption" class="insight-option">
                <label for="dayOfWeekOption">Day of Week Distribution</label>
            </div>
            <div>
                <input type="checkbox" id="weeklyTrendsOption" class="insight-option">
                <label for="weeklyTrendsOption">Weekly Assessment Trends</label>
            </div>
        </div>
        <button id="generatePdfBtn">Generate PDF Report</button>
    </div>

    <!-- Data Preview Section -->
    <div id="dataPreviewSection" style="display: none;">
        <button id="previewDataBtn">Preview Data (15 rows)</button>
        <div id="dataPreviewModal" style="display: none;">
            <div>
                <h5>Data Preview</h5>
                <button id="closePreviewBtn">Close</button>
                <div id="dataPreviewContent"></div>
            </div>
        </div>
    </div>

    <!-- Analytics Results Section -->
    <div id="analyticsResults" style="display: none;">
        <!-- Unique Patients Section -->
        <div id="uniquePatientsSection" class="insight-section">
            <h4>Unique Patients</h4>
            <h1 id="uniquePatientsCount">0</h1>
            <div id="uniquePatientsChart"></div>
            <p class="info-text">This section shows the total number of unique patients and their distribution.</p>
        </div>

        <!-- Assessment Methods Section -->
        <div id="assessmentMethodsSection" class="insight-section">
            <h4>Assessment Methods Distribution</h4>
            <div id="assessmentMethodsChart"></div>
            <p class="info-text">This chart shows the distribution of assessment methods used.</p>
        </div>

        <!-- Completion Time Section -->
        <div id="completionTimeSection" class="insight-section">
            <h4>Average Completion Time</h4>
            <h1 id="avgCompletionTime">0</h1>
            <div id="completionTimeChart"></div>
            <p class="info-text">This section shows the average time taken to complete assessments.</p>
        </div>

        <!-- Protocols Section -->
        <div id="protocolsSection" class="insight-section">
            <h4>Most Used Protocols</h4>
            <div id="protocolsChart"></div>
            <p class="info-text">This chart shows the most frequently used protocols.</p>
        </div>

        <!-- Practitioner Activity Section -->
        <div id="practitionerSection" class="insight-section">
            <h4>Practitioner Activity</h4>
            <div id="practitionerChart"></div>
            <p class="info-text">This section shows the distribution of assessments by practitioner.</p>
        </div>

        <!-- Age Distribution Section -->
        <div id="ageDistributionSection" class="insight-section">
            <h4>Patient Age Distribution</h4>
            <div id="ageDistributionChart"></div>
            <p class="info-text">This chart shows the age distribution of patients.</p>
        </div>

        <!-- Completion Rate Section -->
        <div id="completionRateSection" class="insight-section">
            <h4>Assessment Completion Rate</h4>
            <h1 id="completionRate">0%</h1>
            <div id="completionRateChart"></div>
            <p class="info-text">This section shows the percentage of assessments that were completed.</p>
        </div>

        <!-- Monthly Trends Section -->
        <div id="monthlyTrendsSection" class="insight-section">
            <h4>Monthly Assessment Trends</h4>
            <div id="monthlyTrendsChart"></div>
            <p class="info-text">This chart shows the trend of assessments over months.</p>
        </div>

        <!-- Score Distribution Section -->
        <div id="scoreDistributionSection" class="insight-section">
            <h4>Score Distribution</h4>
            <div id="scoreDistributionChart"></div>
            <p class="info-text">This chart shows the distribution of scores across all assessments.</p>
        </div>

        <!-- Time of Day Section -->
        <div id="timeOfDaySection" class="insight-section">
            <h4>Time of Day Distribution</h4>
            <div id="timeOfDayChart"></div>
            <p class="info-text">This chart shows when assessments are most commonly taken throughout the day.</p>
        </div>

        <!-- Day of Week Section -->
        <div id="dayOfWeekSection" class="insight-section">
            <h4>Day of Week Distribution</h4>
            <div id="dayOfWeekChart"></div>
            <p class="info-text">This chart shows which days of the week have the highest assessment activity.</p>
        </div>

        <!-- Weekly Trends Section -->
        <div id="weeklyTrendsSection" class="insight-section">
            <h4>Weekly Assessment Trends</h4>
            <div id="weeklyTrendsChart"></div>
            <p class="info-text">This chart shows assessment trends over weeks.</p>
        </div>
    </div>
    <!-- Required Libraries (CDN links) -->
    <!-- These can be replaced with local files if needed -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-plugin-datalabels/2.0.0/chartjs-plugin-datalabels.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.2/papaparse.min.js"></script>

    <!-- Core JavaScript -->
    <script>
    /* SEGMENT 2: GLOBAL VARIABLES AND INITIALIZATION */
    // Global variables
    let csvData = [];
    let jsonData = null;
    let reportTitle = 'CSV Analytics Report';
    let reportDate = new Date().toLocaleDateString();
    let clinicName = 'All Clinics';

    // Creyos brand colors
    const creyosColors = {
        iconBlue: '#12C0DB',
        teal: '#4393A7',
        accessibleTeal: '#0E7A95',
        darkBlue: '#3A4661',
        accentBlue: '#BDF2FF',
        accentBlue80: '#CAF5FF',
        accentBlue60: '#D7F7FF',
        accentBlue40: '#E5FAFF'
    };

    // Get Creyos brand colors for charts
    function getCreyosChartColors(count = 8) {
        const colors = [
            creyosColors.iconBlue,
            creyosColors.teal,
            creyosColors.accessibleTeal,
            creyosColors.darkBlue,
            creyosColors.accentBlue,
            creyosColors.accentBlue80,
            creyosColors.accentBlue60,
            creyosColors.accentBlue40
        ];

        // If we need more colors, repeat the pattern
        if (count > colors.length) {
            const repeats = Math.ceil(count / colors.length);
            return Array(repeats).fill(colors).flat().slice(0, count);
        }

        return colors.slice(0, count);
    }

    // Convert hex color to RGB
    function hexToRgb(hex) {
        // Remove the hash if it exists
        hex = hex.replace(/^#/, '');

        // Parse the hex values
        let r, g, b;
        if (hex.length === 3) {
            // For shorthand hex (e.g., #ABC)
            r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
            g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
            b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
        } else {
            // For full hex (e.g., #AABBCC)
            r = parseInt(hex.substring(0, 2), 16);
            g = parseInt(hex.substring(2, 4), 16);
            b = parseInt(hex.substring(4, 6), 16);
        }

        // Return RGB values as a string
        return `${r}, ${g}, ${b}`;
    }

    // Initialize event listeners when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Upload button event listener
        document.getElementById('uploadBtnMain').addEventListener('click', handleFileUpload);

        // Filter buttons event listeners
        document.getElementById('applyFiltersBtn').addEventListener('click', applyFilters);
        document.getElementById('resetFiltersBtn').addEventListener('click', resetFilters);

        // Report options buttons event listeners
        document.getElementById('selectAllBtn').addEventListener('click', selectAllInsights);
        document.getElementById('deselectAllBtn').addEventListener('click', deselectAllInsights);
        document.getElementById('generatePdfBtn').addEventListener('click', generatePDF);

        // Data preview button event listener
        document.getElementById('previewDataBtn').addEventListener('click', showDataPreview);
        document.getElementById('closePreviewBtn').addEventListener('click', closeDataPreview);

        // Initialize all insight options to be deselected
        deselectAllInsights();
    });

    /* SEGMENT 3: CSV PARSING AND DATA PROCESSING */
    // Handle file upload and CSV parsing
    function handleFileUpload() {
        const fileInput = document.getElementById('csvFileMain');
        const file = fileInput.files[0];

        if (!file) {
            alert('Please select a CSV file to upload.');
            return;
        }

        // Show loading indicator
        document.getElementById('uploadBtnMain').textContent = 'Processing...';

        // Parse CSV file using PapaParse
        Papa.parse(file, {
            header: true,
            skipEmptyLines: true,
            complete: function(results) {
                csvData = results.data;
                processCSVData(csvData);

                // Reset button text
                document.getElementById('uploadBtnMain').textContent = 'Upload & Analyze';

                // Show filter and report sections
                document.getElementById('filterSection').style.display = 'block';
                document.getElementById('reportOptionsSection').style.display = 'block';
                document.getElementById('dataPreviewSection').style.display = 'block';
                document.getElementById('analyticsResults').style.display = 'block';

                // Generate all charts
                generateAllCharts();
            },
            error: function(error) {
                console.error('Error parsing CSV:', error);
                alert('Error parsing CSV file. Please check the file format.');
                document.getElementById('uploadBtnMain').textContent = 'Upload & Analyze';
            }
        });
    }

    // Process CSV data and convert to JSON
    function processCSVData(data) {
        // Convert CSV data to JSON format
        jsonData = data.map(row => {
            // Process each row to ensure consistent data format
            return {
                patientId: row['Patient ID'] || row['PatientID'] || '',
                assessmentId: row['Assessment ID'] || row['AssessmentID'] || '',
                protocol: row['Protocol'] || row['ProtocolName'] || '',
                completed: (row['Completed'] === 'Yes' || row['Completed'] === 'true' || row['Completed'] === '1'),
                timestamp: row['Timestamp'] || row['Date'] || '',
                practitioner: row['Practitioner'] || row['PractitionerName'] || '',
                method: row['Method'] || row['AssessmentMethod'] || '',
                score: parseFloat(row['Score'] || '0') || 0,
                completionTime: parseInt(row['CompletionTime'] || '0') || 0,
                birthdate: row['Birthdate'] || row['DOB'] || '',
                gender: row['Gender'] || '',
                clinic: row['Clinic'] || row['ClinicName'] || ''
            };
        });

        // Extract clinic name from the first row if available
        if (jsonData.length > 0 && jsonData[0].clinic) {
            clinicName = jsonData[0].clinic;
        }

        // Set report date to current date
        reportDate = new Date().toLocaleDateString();

        console.log('Processed data:', jsonData);
    }

    // Format hour for chart labels
    function formatHour(hour) {
        // Format as 12-hour time with AM/PM
        return hour === 0 ? '12 AM' :
               hour === 12 ? '12 PM' :
               hour < 12 ? `${hour} AM` :
               `${hour - 12} PM`;
    }

    // Get patient age from birthdate
    function calculateAge(birthdate) {
        if (!birthdate) return null;

        // Try different date formats
        let dob;
        if (birthdate.includes('/')) {
            // MM/DD/YYYY format
            const parts = birthdate.split('/');
            dob = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (birthdate.includes('-')) {
            // YYYY-MM-DD format
            dob = new Date(birthdate);
        } else {
            return null;
        }

        // Check if date is valid
        if (isNaN(dob.getTime())) return null;

        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        const monthDiff = today.getMonth() - dob.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
            age--;
        }

        return age;
    }

    // Extract protocol name without timestamp
    function extractProtocolName(protocol) {
        if (!protocol) return 'Unknown';

        // Remove timestamp if present (e.g., "2023-01-01 Protocol Name")
        const timestampRegex = /^\d{4}-\d{2}-\d{2}\s+|^\d{2}\/\d{2}\/\d{4}\s+/;
        return protocol.replace(timestampRegex, '');
    }

    // Categorize protocol (ADHD, MCI, or Normal)
    function categorizeProtocol(protocol, completionRatio) {
        if (!protocol) return 'Normal';

        const protocolName = extractProtocolName(protocol).toLowerCase();

        // ADHD protocols contain "sar"
        if (protocolName.includes('sar')) {
            return 'ADHD';
        }

        // MCI protocols include IADL and/or IQCODE with specific completion ratios
        if ((protocolName.includes('iadl') || protocolName.includes('iqcode')) &&
            (completionRatio === '3/2' || completionRatio === '4/2')) {
            return 'MCI';
        }

        // All other protocols are Normal
        return 'Normal';
    }

    // Calculate assessment completion ratio
    function calculateCompletionRatio(data) {
        // Count total assessments and completed assessments
        const total = data.length;
        const completed = data.filter(item => item.completed).length;

        // Calculate ratio (e.g., 3/2, 4/2, 1/1)
        if (completed === total) {
            return `${completed}/${total}`;
        } else if (completed > total / 2) {
            return `${completed}/${Math.floor(total / 2)}`;
        } else {
            return `${completed}/${total}`;
        }
    }

    /* SEGMENT 4: FILTERING AND DATA PREVIEW */
    // Filter data based on selected filters
    function filterData() {
        if (!jsonData) return [];

        // Get filter values
        const completionFilter = document.getElementById('completionFilter').value;
        const dateRangeFilter = document.getElementById('dateRangeFilter').value;

        // Apply filters
        return jsonData.filter(item => {
            // Apply completion filter
            if (completionFilter === 'completed' && !item.completed) return false;
            if (completionFilter === 'incomplete' && item.completed) return false;

            // Apply date range filter
            if (dateRangeFilter !== 'all' && item.timestamp) {
                const itemDate = new Date(item.timestamp);
                if (isNaN(itemDate.getTime())) return true; // Include if date is invalid

                const today = new Date();
                const daysAgo = parseInt(dateRangeFilter);
                const cutoffDate = new Date();
                cutoffDate.setDate(today.getDate() - daysAgo);

                if (itemDate < cutoffDate) return false;
            }

            return true;
        });
    }

    // Apply filters and regenerate charts
    function applyFilters() {
        generateAllCharts();
    }

    // Reset filters to default values
    function resetFilters() {
        document.getElementById('completionFilter').value = 'all';
        document.getElementById('dateRangeFilter').value = 'all';
        generateAllCharts();
    }

    // Show data preview modal
    function showDataPreview() {
        if (!csvData || csvData.length === 0) {
            alert('No data to preview. Please upload a CSV file first.');
            return;
        }

        // Get headers and first 15 rows
        const headers = Object.keys(csvData[0]);
        const previewRows = csvData.slice(0, 15);

        // Create table HTML
        let tableHTML = '<table border="1" style="width:100%; border-collapse: collapse;">';

        // Add header row
        tableHTML += '<tr>';
        headers.forEach(header => {
            tableHTML += `<th style="padding: 8px; text-align: left;">${header}</th>`;
        });
        tableHTML += '</tr>';

        // Create table rows
        previewRows.forEach(row => {
            tableHTML += '<tr>';
            headers.forEach(header => {
                tableHTML += `<td style="padding: 8px;">${row[header] || ''}</td>`;
            });
            tableHTML += '</tr>';
        });

        tableHTML += '</table>';

        // Display the table in the modal
        document.getElementById('dataPreviewContent').innerHTML = tableHTML;
        document.getElementById('dataPreviewModal').style.display = 'block';
    }

    // Close data preview modal
    function closeDataPreview() {
        document.getElementById('dataPreviewModal').style.display = 'none';
    }

    // Select all insight options
    function selectAllInsights() {
        const checkboxes = document.querySelectorAll('.insight-option');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    }

    // Deselect all insight options
    function deselectAllInsights() {
        const checkboxes = document.querySelectorAll('.insight-option');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    /* SEGMENT 5: CHART GENERATION - PART 1 */
    // Generate all charts based on filtered data
    function generateAllCharts() {
        // Get filtered data
        const filteredData = filterData();
        if (!filteredData || filteredData.length === 0) {
            console.warn('No data available for chart generation');
            return;
        }

        // Generate each chart if its section is selected
        generateUniquePatients(filteredData);
        generateAssessmentMethods(filteredData);
        generateCompletionTime(filteredData);
        generateProtocols(filteredData);
        generatePractitionerActivity(filteredData);
        generateAgeDistribution(filteredData);
        generateCompletionRate(filteredData);
        generateMonthlyTrends(filteredData);
        generateScoreDistribution(filteredData);
        generateTimeOfDay(filteredData);
        generateDayOfWeek(filteredData);
        generateWeeklyTrends(filteredData);
    }

    // Create a chart with common configuration
    function createChart(canvasId, type, data, options = {}) {
        // Get the canvas element
        const canvas = document.querySelector(`#${canvasId} canvas`);
        if (!canvas) {
            console.error(`Canvas not found for ${canvasId}`);
            return null;
        }

        // Clear any existing chart
        const existingChart = Chart.getChart(canvas);
        if (existingChart) {
            existingChart.destroy();
        }

        // Default options
        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    display: true
                },
                tooltip: {
                    enabled: true
                },
                datalabels: {
                    display: type === 'pie' || type === 'doughnut',
                    color: '#fff',
                    font: {
                        weight: 'bold'
                    },
                    formatter: (value, ctx) => {
                        const sum = ctx.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                        const percentage = (value * 100 / sum).toFixed(1) + '%';
                        return percentage;
                    }
                }
            }
        };

        // Merge options
        const mergedOptions = { ...defaultOptions, ...options };

        // Create and return the chart
        return new Chart(canvas, {
            type: type,
            data: data,
            options: mergedOptions
        });
    }

    // Generate Unique Patients chart
    function generateUniquePatients(data) {
        // Count unique patients
        const uniquePatientIds = new Set();
        data.forEach(item => {
            if (item.patientId) {
                uniquePatientIds.add(item.patientId);
            }
        });

        const count = uniquePatientIds.size;

        // Update count display
        document.getElementById('uniquePatientsCount').textContent = count;

        // Create chart data
        const chartData = {
            labels: ['Unique Patients', 'Total Assessments'],
            datasets: [{
                data: [count, data.length],
                backgroundColor: [creyosColors.iconBlue, creyosColors.teal],
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('uniquePatientsChart', 'pie', chartData);

        // Show section
        document.getElementById('uniquePatientsSection').style.display = 'block';
    }

    /* SEGMENT 6: CHART GENERATION - PART 2 */
    // Generate Assessment Methods chart
    function generateAssessmentMethods(data) {
        // Count assessment methods
        const methodCounts = {};
        data.forEach(item => {
            const method = item.method || 'Unknown';
            methodCounts[method] = (methodCounts[method] || 0) + 1;
        });

        // Sort methods by count (descending)
        const sortedMethods = Object.entries(methodCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5); // Top 5 methods

        // Prepare chart data
        const labels = sortedMethods.map(item => item[0]);
        const data = sortedMethods.map(item => item[1]);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: getCreyosChartColors(5),
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('assessmentMethodsChart', 'pie', chartData);

        // Show section
        document.getElementById('assessmentMethodsSection').style.display = 'block';
    }

    // Generate Completion Time chart
    function generateCompletionTime(data) {
        // Filter completed assessments with valid completion time
        const completedAssessments = data.filter(item => item.completed && item.completionTime > 0);

        if (completedAssessments.length === 0) {
            console.warn('No completed assessments with valid completion time');
            return;
        }

        // Calculate average completion time
        const totalTime = completedAssessments.reduce((sum, item) => sum + item.completionTime, 0);
        const avgTime = Math.round(totalTime / completedAssessments.length);

        // Update average time display (format as minutes:seconds)
        const minutes = Math.floor(avgTime / 60);
        const seconds = avgTime % 60;
        document.getElementById('avgCompletionTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        // Create histogram bins (0-1min, 1-2min, 2-3min, etc.)
        const bins = {};
        const maxBin = 10; // Max 10 minutes

        for (let i = 0; i <= maxBin; i++) {
            bins[i] = 0;
        }

        // Count assessments in each bin
        completedAssessments.forEach(item => {
            const minutes = Math.floor(item.completionTime / 60);
            const bin = Math.min(minutes, maxBin);
            bins[bin]++;
        });

        // Prepare chart data
        const binLabels = Object.keys(bins).map(bin =>
            bin < maxBin ? `${bin}-${parseInt(bin)+1} min` : `${maxBin}+ min`
        );
        const binCounts = Object.values(bins);

        // Create chart data
        const chartData = {
            labels: binLabels,
            datasets: [{
                label: 'Number of Assessments',
                data: binCounts,
                backgroundColor: creyosColors.accessibleTeal,
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('completionTimeChart', 'bar', chartData, {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Assessments'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Completion Time'
                    }
                }
            }
        });

        // Show section
        document.getElementById('completionTimeSection').style.display = 'block';
    }

    /* SEGMENT 7: CHART GENERATION - PART 3 */
    // Generate Protocols chart
    function generateProtocols(data) {
        // Extract and count protocols
        const protocolCounts = {};
        const protocolCategories = { 'ADHD': 0, 'MCI': 0 };

        // Group data by protocol
        const protocolGroups = {};
        data.forEach(item => {
            const protocol = extractProtocolName(item.protocol);
            if (!protocolGroups[protocol]) {
                protocolGroups[protocol] = [];
            }
            protocolGroups[protocol].push(item);
        });

        // Process each protocol group
        Object.entries(protocolGroups).forEach(([protocol, items]) => {
            // Calculate completion ratio for categorization
            const completionRatio = calculateCompletionRatio(items);
            const category = categorizeProtocol(protocol, completionRatio);

            // Count by category or individual protocol
            if (category === 'ADHD' || category === 'MCI') {
                protocolCategories[category]++;
            } else {
                protocolCounts[protocol] = (protocolCounts[protocol] || 0) + items.length;
            }
        });

        // Sort protocols by count (descending)
        const sortedProtocols = Object.entries(protocolCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 8); // Top 8 protocols

        // Prepare chart data
        let labels = [];
        let data = [];

        // Add categories first
        Object.entries(protocolCategories).forEach(([category, count]) => {
            if (count > 0) {
                labels.push(category);
                data.push(count);
            }
        });

        // Add individual protocols
        sortedProtocols.forEach(([protocol, count]) => {
            labels.push(protocol);
            data.push(count);
        });

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Usage Count',
                data: data,
                backgroundColor: function(context) {
                    const label = context.chart.data.labels[context.dataIndex];
                    // Special colors for categorized protocols
                    if (label === 'ADHD') {
                        return creyosColors.iconBlue; // Creyos Icon Blue for ADHD
                    } else if (label === 'MCI') {
                        return creyosColors.accentBlue; // Creyos Accent Blue for MCI
                    }
                    // Alternate colors for other protocols using only Creyos brand colors
                    return context.dataIndex % 2 === 0 ? creyosColors.teal : creyosColors.darkBlue; // Alternate Teal and Dark Blue
                },
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('protocolsChart', 'bar', chartData, {
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Assessments'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        });

        // Show section
        document.getElementById('protocolsSection').style.display = 'block';
    }

    // Generate Practitioner Activity chart
    function generatePractitionerActivity(data) {
        // Count assessments by practitioner
        const practitionerCounts = {};
        data.forEach(item => {
            const practitioner = item.practitioner || 'Unknown';
            practitionerCounts[practitioner] = (practitionerCounts[practitioner] || 0) + 1;
        });

        // Sort practitioners by count (descending)
        const sortedPractitioners = Object.entries(practitionerCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10); // Top 10 practitioners

        // Prepare chart data
        const labels = sortedPractitioners.map(item => item[0]);
        const data = sortedPractitioners.map(item => item[1]);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Number of Assessments',
                data: data,
                backgroundColor: getCreyosChartColors(labels.length),
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('practitionerChart', 'pie', chartData);

        // Show section
        document.getElementById('practitionerSection').style.display = 'block';
    }

    /* SEGMENT 8: CHART GENERATION - PART 4 */
    // Generate Age Distribution chart
    function generateAgeDistribution(data) {
        // Calculate age for each patient
        const patientAges = {};
        data.forEach(item => {
            if (item.patientId && item.birthdate) {
                const age = calculateAge(item.birthdate);
                if (age !== null) {
                    patientAges[item.patientId] = age;
                }
            }
        });

        // If no valid ages, skip this chart
        if (Object.keys(patientAges).length === 0) {
            console.warn('No valid patient ages found');
            return;
        }

        // Create age bins (0-9, 10-19, 20-29, etc.)
        const ageBins = {};
        for (let i = 0; i < 10; i++) {
            const binStart = i * 10;
            const binEnd = binStart + 9;
            ageBins[`${binStart}-${binEnd}`] = 0;
        }
        ageBins['90+'] = 0;

        // Count patients in each age bin
        Object.values(patientAges).forEach(age => {
            if (age >= 90) {
                ageBins['90+']++;
            } else {
                const bin = Math.floor(age / 10);
                const binKey = `${bin*10}-${bin*10+9}`;
                ageBins[binKey]++;
            }
        });

        // Prepare chart data
        const labels = Object.keys(ageBins);
        const data = Object.values(ageBins);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Number of Patients',
                data: data,
                backgroundColor: creyosColors.teal,
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('ageDistributionChart', 'bar', chartData, {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Patients'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Age Range'
                    }
                }
            }
        });

        // Show section
        document.getElementById('ageDistributionSection').style.display = 'block';
    }

    // Generate Completion Rate chart
    function generateCompletionRate(data) {
        // Count completed assessments
        const completed = data.filter(item => item.completed).length;
        const total = data.length;

        // Calculate completion rate percentage
        const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

        // Update completion rate display
        document.getElementById('completionRate').textContent = `${completionRate}%`;

        // Create chart data
        const chartData = {
            labels: ['Completed', 'Not Completed'],
            datasets: [{
                data: [completed, total - completed],
                backgroundColor: [creyosColors.iconBlue, creyosColors.darkBlue],
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('completionRateChart', 'doughnut', chartData);

        // Show section
        document.getElementById('completionRateSection').style.display = 'block';
    }

    /* SEGMENT 9: CHART GENERATION - PART 5 */
    // Generate Monthly Trends chart
    function generateMonthlyTrends(data) {
        // Group assessments by month
        const monthlyData = {};

        // Initialize all months for the past year
        const today = new Date();
        for (let i = 0; i < 12; i++) {
            const date = new Date(today);
            date.setMonth(today.getMonth() - i);
            const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
            monthlyData[monthKey] = 0;
        }

        // Count assessments by month
        data.forEach(item => {
            if (item.timestamp) {
                const date = new Date(item.timestamp);
                if (!isNaN(date.getTime())) {
                    const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                    if (monthlyData[monthKey] !== undefined) {
                        monthlyData[monthKey]++;
                    }
                }
            }
        });

        // Sort months chronologically
        const sortedMonths = Object.entries(monthlyData)
            .sort((a, b) => a[0].localeCompare(b[0]));

        // Format month labels (e.g., "Jan 2023")
        const labels = sortedMonths.map(([month]) => {
            const [year, monthNum] = month.split('-');
            const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1);
            return date.toLocaleDateString(undefined, { month: 'short', year: 'numeric' });
        });

        const data = sortedMonths.map(([, count]) => count);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Number of Assessments',
                data: data,
                backgroundColor: `rgba(${hexToRgb(creyosColors.iconBlue)}, 0.7)`,
                borderColor: creyosColors.iconBlue,
                borderWidth: 1,
                tension: 0.3,
                fill: true
            }]
        };

        // Create chart
        createChart('monthlyTrendsChart', 'line', chartData, {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Assessments'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Month'
                    }
                }
            }
        });

        // Show section
        document.getElementById('monthlyTrendsSection').style.display = 'block';
    }

    // Generate Score Distribution chart
    function generateScoreDistribution(data) {
        // Filter assessments with valid scores
        const validScores = data.filter(item => item.completed && !isNaN(item.score) && item.score >= 0);

        if (validScores.length === 0) {
            console.warn('No valid scores found');
            return;
        }

        // Create score bins (0-9, 10-19, 20-29, etc.)
        const scoreBins = {};
        for (let i = 0; i < 10; i++) {
            const binStart = i * 10;
            const binEnd = binStart + 9;
            scoreBins[`${binStart}-${binEnd}`] = 0;
        }
        scoreBins['100'] = 0;

        // Count scores in each bin
        validScores.forEach(item => {
            const score = Math.min(Math.max(0, Math.round(item.score)), 100);
            if (score === 100) {
                scoreBins['100']++;
            } else {
                const bin = Math.floor(score / 10);
                const binKey = `${bin*10}-${bin*10+9}`;
                scoreBins[binKey]++;
            }
        });

        // Prepare chart data
        const labels = Object.keys(scoreBins);
        const data = Object.values(scoreBins);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Number of Scores',
                data: data,
                backgroundColor: creyosColors.darkBlue,
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('scoreDistributionChart', 'bar', chartData, {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Assessments'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Score Range'
                    }
                }
            }
        });

        // Show section
        document.getElementById('scoreDistributionSection').style.display = 'block';
    }

    /* SEGMENT 10: CHART GENERATION - PART 6 */
    // Generate Time of Day chart
    function generateTimeOfDay(data) {
        // Initialize hours (0-23)
        const hourCounts = {};
        for (let i = 0; i < 24; i++) {
            hourCounts[i] = 0;
        }

        // Count assessments by hour
        data.forEach(item => {
            if (item.timestamp) {
                const date = new Date(item.timestamp);
                if (!isNaN(date.getTime())) {
                    const hour = date.getHours();
                    hourCounts[hour]++;
                }
            }
        });

        // Prepare chart data
        const labels = Object.keys(hourCounts).map(hour => formatHour(parseInt(hour)));
        const data = Object.values(hourCounts);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Number of Assessments',
                data: data,
                backgroundColor: `rgba(${hexToRgb(creyosColors.iconBlue)}, 0.7)`, // Creyos Icon Blue with transparency
                borderColor: creyosColors.iconBlue,
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('timeOfDayChart', 'bar', chartData, {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Assessments'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Time of Day'
                    }
                }
            }
        });

        // Show section
        document.getElementById('timeOfDaySection').style.display = 'block';
    }

    // Generate Day of Week chart
    function generateDayOfWeek(data) {
        // Initialize days (0-6, Sunday-Saturday)
        const dayCounts = {
            0: 0, // Sunday
            1: 0, // Monday
            2: 0, // Tuesday
            3: 0, // Wednesday
            4: 0, // Thursday
            5: 0, // Friday
            6: 0  // Saturday
        };

        // Count assessments by day of week
        data.forEach(item => {
            if (item.timestamp) {
                const date = new Date(item.timestamp);
                if (!isNaN(date.getTime())) {
                    const day = date.getDay();
                    dayCounts[day]++;
                }
            }
        });

        // Prepare chart data with day names
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const labels = dayNames;
        const data = dayNames.map((_, index) => dayCounts[index]);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Number of Assessments',
                data: data,
                backgroundColor: getCreyosChartColors(7),
                borderWidth: 1
            }]
        };

        // Create chart
        createChart('dayOfWeekChart', 'pie', chartData);

        // Show section
        document.getElementById('dayOfWeekSection').style.display = 'block';
    }

    // Generate Weekly Trends chart
    function generateWeeklyTrends(data) {
        // Group assessments by week
        const weeklyData = {};

        // Initialize recent weeks (last 10 weeks)
        const today = new Date();
        for (let i = 0; i < 10; i++) {
            const date = new Date(today);
            date.setDate(today.getDate() - (i * 7));
            const year = date.getFullYear();
            const weekNum = getWeekNumber(date);
            const weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;
            weeklyData[weekKey] = 0;
        }

        // Count assessments by week
        data.forEach(item => {
            if (item.timestamp) {
                const date = new Date(item.timestamp);
                if (!isNaN(date.getTime())) {
                    const year = date.getFullYear();
                    const weekNum = getWeekNumber(date);
                    const weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;
                    if (weeklyData[weekKey] !== undefined) {
                        weeklyData[weekKey]++;
                    }
                }
            }
        });

        // Sort weeks chronologically
        const sortedWeeks = Object.entries(weeklyData)
            .sort((a, b) => a[0].localeCompare(b[0]));

        // Get recent weeks (last 10 weeks)
        const recentWeeks = sortedWeeks.map(item => item[0]);

        // Format week labels
        const labels = recentWeeks.map(week => {
            const weekNum = week.split('-W')[1];
            return `Week ${weekNum}`;
        });

        const data = sortedWeeks.map(item => item[1]);

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Number of Assessments',
                data: data,
                backgroundColor: `rgba(${hexToRgb(creyosColors.accessibleTeal)}, 0.7)`,
                borderColor: creyosColors.accessibleTeal,
                borderWidth: 1,
                tension: 0.3,
                fill: true
            }]
        };

        // Create chart
        createChart('weeklyTrendsChart', 'line', chartData, {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Assessments'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Week'
                    }
                }
            }
        });

        // Show section
        document.getElementById('weeklyTrendsSection').style.display = 'block';
    }

    // Helper function to get week number
    function getWeekNumber(date) {
        const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
        const dayNum = d.getUTCDay() || 7;
        d.setUTCDate(d.getUTCDate() + 4 - dayNum);
        const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
        return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
    }

    /* SEGMENT 11: PDF GENERATION */
    // Generate PDF report
    function generatePDF() {
        // Check if jsPDF is available
        if (typeof jspdf === 'undefined' || typeof jspdf.jsPDF === 'undefined') {
            alert('PDF generation library not loaded. Please try again later.');
            return;
        }

        // Get selected insights
        const selectedInsights = [];
        document.querySelectorAll('.insight-option:checked').forEach(checkbox => {
            selectedInsights.push(checkbox.id.replace('Option', ''));
        });

        if (selectedInsights.length === 0) {
            alert('Please select at least one insight to include in the report.');
            return;
        }

        // Create PDF document
        const { jsPDF } = jspdf;
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        // Set font
        pdf.setFont('helvetica');

        // Add header
        pdf.setFontSize(22);
        pdf.setTextColor(0, 0, 0);
        pdf.text(reportTitle, 20, 20);

        // Add clinic name and date
        pdf.setFontSize(12);
        pdf.setTextColor(100, 100, 100);
        pdf.text(`Clinic: ${clinicName}`, 20, 30);
        pdf.text(`Date: ${reportDate}`, 20, 35);

        // Add separator line
        pdf.setDrawColor(200, 200, 200);
        pdf.line(20, 40, 190, 40);

        // Initialize Y position for content
        let yPos = 50;

        // Add selected insights
        for (const insight of selectedInsights) {
            // Check if section exists and is visible
            const section = document.getElementById(`${insight}Section`);
            if (!section) continue;

            // Get section title
            const titleElement = section.querySelector('h4');
            const titleText = titleElement ? titleElement.textContent.trim() : '';

            // Add section title
            pdf.setFontSize(16);
            pdf.setTextColor(0, 0, 0);
            pdf.text(titleText, 20, yPos);
            yPos += 10;

            // Add section info text
            const infoText = section.querySelector('.info-text');
            if (infoText) {
                pdf.setFontSize(10);
                pdf.setTextColor(100, 100, 100);
                const text = infoText.textContent.trim();
                const lines = pdf.splitTextToSize(text, 170);
                pdf.text(lines, 20, yPos);
                yPos += lines.length * 5 + 5;
            }

            // Add chart if available
            const chartElement = section.querySelector(`#${insight}Chart`);
            if (chartElement) {
                // Capture chart as image
                const canvas = chartElement.querySelector('canvas');
                if (canvas) {
                    // Check if we need a new page
                    if (yPos > 240) {
                        pdf.addPage();
                        yPos = 20;
                    }

                    // Add chart image
                    const imgData = canvas.toDataURL('image/png');
                    pdf.addImage(imgData, 'PNG', 20, yPos, 170, 80);
                    yPos += 90;
                }
            }

            // Add numeric value if available
            const valueElement = section.querySelector('h1');
            if (valueElement) {
                pdf.setFontSize(14);
                pdf.setTextColor(0, 0, 0);
                pdf.text(`Value: ${valueElement.textContent.trim()}`, 20, yPos);
                yPos += 10;
            }

            // Add separator line
            pdf.setDrawColor(200, 200, 200);
            pdf.line(20, yPos, 190, yPos);
            yPos += 15;

            // Add new page if needed
            if (yPos > 270) {
                pdf.addPage();
                yPos = 20;
            }
        }

        // Add footer
        const pageCount = pdf.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            pdf.setPage(i);
            pdf.setFontSize(10);
            pdf.setTextColor(150, 150, 150);
            pdf.text('Creyos CS 2025', 20, 285);
            pdf.text(`Page ${i} of ${pageCount}`, 180, 285);
        }

        // Save the PDF
        const filename = `${reportTitle.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(filename);
    }
    </script>
</body>
</html>
