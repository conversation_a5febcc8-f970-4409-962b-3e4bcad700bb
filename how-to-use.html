<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; connect-src 'self'; font-src 'self' https://cdnjs.cloudflare.com https://use.typekit.net; img-src 'self' data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.typekit.net; frame-src 'none'; object-src 'none';">
    <!-- Block Cloudflare Insights only -->
    <script>
        // Create a MutationObserver to detect and remove only Cloudflare Insights scripts
        document.addEventListener('DOMContentLoaded', function() {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.tagName === 'SCRIPT' &&
                                (node.src.includes('cloudflareinsights') ||
                                 node.src.includes('beacon.min.js'))) {
                                console.log('Blocked Cloudflare Insights script:', node.src);
                                node.parentNode.removeChild(node);
                            }
                        });
                    }
                });
            });

            observer.observe(document.documentElement, {
                childList: true,
                subtree: true
            });

            // Also check for any existing Cloudflare Insights scripts
            document.querySelectorAll('script').forEach(function(script) {
                if (script.src.includes('cloudflareinsights') ||
                    script.src.includes('beacon.min.js')) {
                    console.log('Removed existing Cloudflare Insights script:', script.src);
                    script.parentNode.removeChild(script);
                }
            });
        });
    </script>
    <title>How to Use - CSV Analytics Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <!-- Theme System -->
    <link rel="stylesheet" href="css/theme.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="images/CreyosLogo.png" alt="Creyos Logo" height="30" class="d-inline-block align-top me-2">
                CSV Analytics Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html"><i class="fas fa-home me-1"></i>Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="how-to-use.html"><i class="fas fa-question-circle me-1"></i>How to Use</a>
                    </li>
                    <li class="nav-item">
                        <span class="nav-link version-display">v1.9.4</span>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-sm btn-outline-light ms-2" id="themeToggle">
                            <i class="fas fa-moon"></i>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h1 class="h3 mb-0"><i class="fas fa-book me-2"></i>How to Use the CSV Analytics Dashboard</h1>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>This guide will help you understand how to use the CSV Analytics Dashboard to analyze your Creyos assessment data.
                        </div>

                        <h2 class="h4 mt-4 mb-3 border-bottom pb-2"><i class="fas fa-file-upload me-2 text-primary"></i>Step 1: Upload Your CSV File</h2>
                        <p>Start by uploading your Creyos assessment data CSV file:</p>
                        <ol>
                            <li>On the home page, click the "Choose File" button in the upload section.</li>
                            <li>Select your CSV file from your computer.</li>
                            <li>Click the "Upload & Analyze" button to process the data.</li>
                        </ol>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>Your data is processed locally in your browser. No data is sent to any server.
                        </div>

                        <h2 class="h4 mt-4 mb-3 border-bottom pb-2"><i class="fas fa-chart-pie me-2 text-success"></i>Step 2: Select Insights for Your Report</h2>
                        <p>After uploading, you can select which insights to include in your report:</p>
                        <ul>
                            <li>Use the checkboxes to select or deselect specific insights.</li>
                            <li>Click "Select All" to include all available insights.</li>
                            <li>Click "Deselect All" to clear all selections.</li>
                        </ul>
                        <p>Available insights include:</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group mb-3">
                                    <li class="list-group-item"><i class="fas fa-users text-primary me-2"></i>Unique Patients Count</li>
                                    <li class="list-group-item"><i class="fas fa-chart-pie text-success me-2"></i>Assessment Methods</li>
                                    <li class="list-group-item"><i class="fas fa-clock text-warning me-2"></i>Completion Time</li>
                                    <li class="list-group-item"><i class="fas fa-list-ol text-danger me-2"></i>Most Used Protocols</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group mb-3">
                                    <li class="list-group-item"><i class="fas fa-user-md text-info me-2"></i>Practitioner Activity</li>
                                    <li class="list-group-item"><i class="fas fa-birthday-cake text-primary me-2"></i>Age Distribution</li>
                                    <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i>Assessment Completion</li>
                                    <li class="list-group-item"><i class="fas fa-calendar-alt text-warning me-2"></i>Monthly Trends</li>
                                </ul>
                            </div>
                        </div>

                        <h2 class="h4 mt-4 mb-3 border-bottom pb-2"><i class="fas fa-info-circle me-2 text-info"></i>Step 3: View Information Boxes</h2>
                        <p>Each section in the report has an information box that provides context and explanation:</p>
                        <ul>
                            <li>Click the <button class="btn btn-sm btn-outline-info"><i class="fas fa-info-circle"></i></button> button next to any section title to toggle the information box.</li>
                            <li>Information boxes provide insights about what the data means and how to interpret it.</li>
                            <li>These information boxes are also included in the PDF report.</li>
                        </ul>

                        <h2 class="h4 mt-4 mb-3 border-bottom pb-2"><i class="fas fa-file-pdf me-2 text-danger"></i>Step 4: Generate PDF Report</h2>
                        <p>Once you've selected your insights, you can generate a PDF report:</p>
                        <ol>
                            <li>Click the "Generate PDF Report" button at the bottom of the selection panel.</li>
                            <li>Wait for the report to be generated (this may take a few seconds).</li>
                            <li>The PDF will automatically download to your computer.</li>
                        </ol>
                        <p>The PDF report includes:</p>
                        <ul>
                            <li>All selected insights with charts and data tables</li>
                            <li>Information boxes explaining each section</li>
                            <li>Clean, professional layout with Creyos branding</li>
                            <li>Date and clinic name for reference</li>
                        </ul>

                        <h2 class="h4 mt-4 mb-3 border-bottom pb-2"><i class="fas fa-table me-2 text-primary"></i>Step 5: Preview Raw Data</h2>
                        <p>If you need to see the raw data from your CSV file:</p>
                        <ul>
                            <li>Click the "Preview Data (15 rows)" button at the top of the analysis section.</li>
                            <li>A modal will open showing the first 15 rows of your data.</li>
                            <li>This can help you verify that your data was imported correctly.</li>
                        </ul>

                        <h2 class="h4 mt-4 mb-3 border-bottom pb-2"><i class="fas fa-filter me-2 text-success"></i>Step 6: Use Filters (Optional)</h2>
                        <p>You can filter your data to focus on specific aspects:</p>
                        <ul>
                            <li>Use the "Completion Status" filter to focus on completed or incomplete assessments.</li>
                            <li>Use the "Date Range" filter to focus on recent data (last 30, 90, 180 days, or year).</li>
                            <li>Filters affect all charts and calculations in real-time.</li>
                            <li>Click "Apply Filters" to update the charts with your selections.</li>
                            <li>Click "Reset Filters" to return to the default view (all data).</li>
                        </ul>

                        <h2 class="h4 mt-4 mb-3 border-bottom pb-2"><i class="fas fa-lightbulb me-2 text-warning"></i>Tips for Best Results</h2>
                        <ul>
                            <li><strong>Use complete data:</strong> The more complete your CSV export, the more insights you'll get.</li>
                            <li><strong>Check for errors:</strong> If charts appear empty, check your data for missing values.</li>
                            <li><strong>Browser compatibility:</strong> This tool works best in Chrome, Firefox, or Edge.</li>
                            <li><strong>Clear cache:</strong> If you experience issues, try clearing your browser cache or visit the <a href="clear-cache.html">Clear Cache</a> page.</li>
                        </ul>

                        <div class="alert alert-success mt-4">
                            <i class="fas fa-check-circle me-2"></i>Remember, all processing happens in your browser. Your data privacy is maintained as no information is sent to any server.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">Creyos CS 2025</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Version Information -->
    <script src="js/version.js?v=********"></script>
    <!-- Theme System -->
    <script src="js/theme.js?v=********"></script>

    <!-- Cache Buster -->
    <script>
        // Force browser to reload scripts
        console.log('Cache buster activated');
        document.querySelectorAll('script[src]').forEach(script => {
            if (script.src.includes('version.js') || script.src.includes('theme.js')) {
                const originalSrc = script.src;
                script.src = originalSrc.includes('?') ? originalSrc : originalSrc + '?v=' + new Date().getTime();
                console.log('Reloaded script:', script.src);
            }
        });
    </script>
</body>
</html>
