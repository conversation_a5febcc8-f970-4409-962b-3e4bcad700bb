/**
 * CSV Analytics Dashboard - Chart System
 *
 * This file contains the chart creation and management system for the CSV Analytics Dashboard.
 * It provides a simple, direct approach to creating and updating charts.
 */

// Register Chart.js DataLabels plugin if available
if (typeof ChartDataLabels !== 'undefined') {
    Chart.register(ChartDataLabels);
    // Set default configuration for DataLabels
    Chart.defaults.set('plugins.datalabels', {
        display: false // Don't show labels by default in the UI
    });
}

// Global chart registry
const ChartRegistry = {
    // Store all chart instances
    instances: {},

    // Create or update a chart
    createChart: function(containerId, type, data, options = {}) {
        console.log(`Creating chart in container: ${containerId}`);

        // Find the container
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Chart container not found: ${containerId}`);
            return null;
        }

        // Create or get canvas
        let canvas = container.querySelector('canvas');
        if (!canvas) {
            console.log(`Creating new canvas in container: ${containerId}`);
            canvas = document.createElement('canvas');
            container.innerHTML = '';
            container.appendChild(canvas);
        }

        // Ensure canvas has dimensions
        if (canvas.width === 0 || canvas.height === 0) {
            canvas.width = container.clientWidth || 300;
            canvas.height = container.clientHeight || 200;
        }

        // Get context
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error(`Could not get 2D context for container: ${containerId}`);
            return null;
        }

        // Destroy existing chart if it exists
        if (this.instances[containerId]) {
            console.log(`Destroying existing chart in container: ${containerId}`);
            this.instances[containerId].destroy();
            delete this.instances[containerId];
        }

        // Default options
        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false
        };

        // Create new chart
        try {
            console.log(`Initializing new chart in container: ${containerId}`);

            // Creyos brand color palette
            const chartColors = [
                '#12C0DB', // Creyos Icon Blue
                '#4393A7', // Creyos Teal
                '#0E7A95', // Creyos Accessible Teal
                '#3A4661', // Creyos Dark Blue
                '#BDF2FF', // Creyos Accent Blue
                '#CAF5FF', // Creyos Accent Blue 80%
                '#D7F7FF', // Creyos Accent Blue 60%
                '#E5FAFF'  // Creyos Accent Blue 40%
            ];

            // We're using a modern theme, not dark mode
            const isDark = false;

            // Set Creyos theme chart options
            const creyosOptions = {
                color: '#3A4661', // Creyos Dark Blue
                borderColor: '#BDF2FF', // Creyos Accent Blue
                font: {
                    family: "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                },
                grid: {
                    color: 'rgba(18, 192, 219, 0.1)' // Creyos Icon Blue with low opacity
                }
            };

            // Create the chart with theme-aware options
            this.instances[containerId] = new Chart(ctx, {
                type: type,
                data: data,
                options: {
                    ...defaultOptions,
                    ...options,
                    scales: {
                        ...options.scales,
                        x: {
                            ...options.scales?.x,
                            grid: {
                                color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(18, 192, 219, 0.1)', // Creyos Icon Blue with low opacity
                                ...options.scales?.x?.grid
                            },
                            ticks: {
                                color: isDark ? '#adb5bd' : '#3A4661', // Creyos Dark Blue
                                ...options.scales?.x?.ticks
                            }
                        },
                        y: {
                            ...options.scales?.y,
                            grid: {
                                color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(18, 192, 219, 0.1)', // Creyos Icon Blue with low opacity
                                ...options.scales?.y?.grid
                            },
                            ticks: {
                                color: isDark ? '#adb5bd' : '#3A4661', // Creyos Dark Blue
                                ...options.scales?.y?.ticks
                            }
                        }
                    },
                    plugins: {
                        ...options.plugins,
                        legend: {
                            ...options.plugins?.legend,
                            labels: {
                                color: isDark ? '#f8f9fa' : '#3A4661', // Creyos Dark Blue
                                ...options.plugins?.legend?.labels
                            }
                        },
                        title: {
                            ...options.plugins?.title,
                            color: isDark ? '#f8f9fa' : '#3A4661', // Creyos Dark Blue
                        }
                    }
                }
            });

            console.log(`Chart created successfully in container: ${containerId}`);
            return this.instances[containerId];
        } catch (error) {
            console.error(`Error creating chart in container: ${containerId}`, error);
            return null;
        }
    },

    // Remove a chart
    removeChart: function(containerId) {
        if (this.instances[containerId]) {
            this.instances[containerId].destroy();
            delete this.instances[containerId];
            return true;
        }
        return false;
    },

    // Remove all charts
    removeAllCharts: function() {
        for (const id in this.instances) {
            this.removeChart(id);
        }
    },

    // Initialize all chart containers
    initializeContainers: function() {
        const containers = document.querySelectorAll('[id$="Chart"]');
        console.log(`Found ${containers.length} chart containers`);

        containers.forEach(container => {
            // Ensure container has a canvas
            if (!container.querySelector('canvas')) {
                const canvas = document.createElement('canvas');
                canvas.width = container.clientWidth || 300;
                canvas.height = container.clientHeight || 200;
                container.innerHTML = '';
                container.appendChild(canvas);
                console.log(`Created canvas in container: ${container.id}`);
            }
        });

        return containers.length;
    },

    // Update all charts with modern colors
    updateChartsTheme: function() {
        console.log('Updating charts with modern colors...');

        // Creyos brand color palette
        const chartColors = [
            '#12C0DB', // Creyos Icon Blue
            '#4393A7', // Creyos Teal
            '#0E7A95', // Creyos Accessible Teal
            '#3A4661', // Creyos Dark Blue
            '#BDF2FF', // Creyos Accent Blue
            '#CAF5FF', // Creyos Accent Blue 80%
            '#D7F7FF', // Creyos Accent Blue 60%
            '#E5FAFF'  // Creyos Accent Blue 40%
        ];

        // Update Chart.js defaults
        if (window.Chart) {
            Chart.defaults.color = '#3A4661'; // Creyos Dark Blue
            Chart.defaults.borderColor = '#BDF2FF'; // Creyos Accent Blue
            Chart.defaults.font.family = "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
        }

        // Update all existing charts
        Object.keys(this.instances).forEach(chartId => {
            if (this.instances[chartId]) {
                console.log(`Updating chart: ${chartId} with modern colors`);

                // Update chart options
                const chart = this.instances[chartId];

                // Update datasets with modern colors
                if (chart.data && chart.data.datasets) {
                    chart.data.datasets.forEach((dataset, index) => {
                        // For line charts
                        if (chart.config.type === 'line') {
                            dataset.borderColor = chartColors[index % chartColors.length];
                            dataset.backgroundColor = `${chartColors[index % chartColors.length]}20`; // 20% opacity
                            dataset.pointBackgroundColor = chartColors[index % chartColors.length];
                            dataset.pointBorderColor = '#fff';
                            dataset.pointHoverBackgroundColor = '#fff';
                            dataset.pointHoverBorderColor = chartColors[index % chartColors.length];
                        }
                        // For bar/column charts
                        else if (chart.config.type === 'bar') {
                            dataset.backgroundColor = chartColors[index % chartColors.length];
                            dataset.hoverBackgroundColor = chartColors[(index + 4) % chartColors.length];
                        }
                        // For pie/doughnut charts
                        else if (chart.config.type === 'pie' || chart.config.type === 'doughnut') {
                            dataset.backgroundColor = chartColors;
                            dataset.hoverBackgroundColor = chartColors.map(color => {
                                // Lighten the color slightly for hover
                                return color + 'dd'; // 85% opacity
                            });
                        }
                    });
                }

                // Update scales
                if (chart.options.scales) {
                    // Update X axis
                    if (chart.options.scales.x) {
                        if (chart.options.scales.x.grid) {
                            chart.options.scales.x.grid.color = 'rgba(0, 0, 0, 0.05)';
                        }
                        if (chart.options.scales.x.ticks) {
                            chart.options.scales.x.ticks.color = '#6c757d';
                            chart.options.scales.x.ticks.font = {
                                family: "'Inter', sans-serif",
                                size: 11
                            };
                        }
                    }

                    // Update Y axis
                    if (chart.options.scales.y) {
                        if (chart.options.scales.y.grid) {
                            chart.options.scales.y.grid.color = 'rgba(0, 0, 0, 0.05)';
                        }
                        if (chart.options.scales.y.ticks) {
                            chart.options.scales.y.ticks.color = '#6c757d';
                            chart.options.scales.y.ticks.font = {
                                family: "'Inter', sans-serif",
                                size: 11
                            };
                        }
                    }
                }

                // Update plugins
                if (chart.options.plugins) {
                    // Update legend
                    if (chart.options.plugins.legend) {
                        chart.options.plugins.legend.labels = {
                            color: '#212529',
                            font: {
                                family: "'Inter', sans-serif",
                                size: 12,
                                weight: 500
                            },
                            padding: 15,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        };
                    }

                    // Update title
                    if (chart.options.plugins.title) {
                        chart.options.plugins.title.color = '#212529';
                        chart.options.plugins.title.font = {
                            family: "'Inter', sans-serif",
                            size: 14,
                            weight: 600
                        };
                        chart.options.plugins.title.padding = {
                            top: 10,
                            bottom: 20
                        };
                    }
                }

                // Update the chart
                chart.update();
            }
        });

        console.log('Charts updated with modern colors');
    }
};

// Initialize containers when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    ChartRegistry.initializeContainers();
});

// Also initialize when the window loads (as a backup)
window.addEventListener('load', function() {
    ChartRegistry.initializeContainers();
});
