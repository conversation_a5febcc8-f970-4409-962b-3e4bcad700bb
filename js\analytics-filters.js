/**
 * CSV Analytics Dashboard - Analytics Filters
 *
 * This file contains filter functionality for the CSV Analytics Dashboard.
 * These features provide more control over data visualization and insights.
 */

// Create a filter for assessment completion status
function createCompletionFilter() {
    try {
        console.log('Creating completion filter...');

        // Create filter UI with collapsible functionality
        const filterContainer = document.createElement('div');
        filterContainer.className = 'card shadow-sm mb-4 filter-container';
        filterContainer.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center" style="cursor: pointer;" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                <h5 class="card-title mb-0">Filter Data</h5>
                <i class="fas fa-chevron-down filter-toggle-icon"></i>
            </div>
            <div class="collapse" id="filterCollapse">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="completionFilter">Completion Status:</label>
                                <select id="completionFilter" class="form-select">
                                    <option value="all">All Assessments</option>
                                    <option value="completed">Completed Only</option>
                                    <option value="incomplete">Incomplete Only</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="dateRangeFilter">Quick Date Range:</label>
                                <select id="dateRangeFilter" class="form-select">
                                    <option value="all">All Time</option>
                                    <option value="30">Last 30 Days</option>
                                    <option value="90">Last 90 Days</option>
                                    <option value="180">Last 180 Days</option>
                                    <option value="365">Last Year</option>
                                    <option value="custom">Custom Range</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div id="customDateRange" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="startDateFilter">Start Date:</label>
                                            <input type="date" id="startDateFilter" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="endDateFilter">End Date:</label>
                                            <input type="date" id="endDateFilter" class="form-control">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <button id="applyFilters" class="btn btn-primary">Apply Filters</button>
                            <button id="resetFilters" class="btn btn-outline-secondary ms-2">Reset</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Insert filter at the top of the analysis section
        const analysisSection = document.getElementById('analysisSection');
        if (analysisSection) {
            analysisSection.insertBefore(filterContainer, analysisSection.firstChild);

            // Add event listeners
            document.getElementById('applyFilters').addEventListener('click', applyDataFilters);
            document.getElementById('resetFilters').addEventListener('click', resetDataFilters);

            // Add event listener for date range filter change
            document.getElementById('dateRangeFilter').addEventListener('change', function() {
                const customDateRange = document.getElementById('customDateRange');
                if (this.value === 'custom') {
                    customDateRange.style.display = 'block';
                } else {
                    customDateRange.style.display = 'none';
                }
            });
        }

        console.log('Completion filter created');
    } catch (error) {
        console.error('Error in createCompletionFilter:', error);
    }
}

// Apply data filters
function applyDataFilters() {
    try {
        console.log('Applying data filters...');

        const completionFilter = document.getElementById('completionFilter').value;
        const dateRangeFilter = document.getElementById('dateRangeFilter').value;

        // Filter the data
        const filteredData = csvData.filter(row => {
            let includeRow = true;

            // Apply completion filter
            if (completionFilter !== 'all') {
                if (row['Assessments Completed']) {
                    const parts = row['Assessments Completed'].split('/');
                    if (parts.length === 2) {
                        const [done, all] = parts.map(Number);
                        if (!isNaN(done) && !isNaN(all)) {
                            const isCompleted = done >= all || (done >= 3 && all >= 2 && done > all - 1);
                            if (completionFilter === 'completed' && !isCompleted) {
                                includeRow = false;
                            } else if (completionFilter === 'incomplete' && isCompleted) {
                                includeRow = false;
                            }
                        }
                    }
                }
            }

            // Apply date range filter
            if (row['Time administered at']) {
                const date = new Date(row['Time administered at']);
                if (!isNaN(date)) {
                    if (dateRangeFilter === 'custom') {
                        // Apply custom date range filter
                        if (startDateFilter) {
                            const startDate = new Date(startDateFilter);
                            startDate.setHours(0, 0, 0, 0); // Start of day
                            if (date < startDate) {
                                includeRow = false;
                            }
                        }

                        if (endDateFilter && includeRow) {
                            const endDate = new Date(endDateFilter);
                            endDate.setHours(23, 59, 59, 999); // End of day
                            if (date > endDate) {
                                includeRow = false;
                            }
                        }
                    } else if (dateRangeFilter !== 'all') {
                        // Apply predefined date range filter
                        const today = new Date();
                        const daysDiff = (today - date) / (1000 * 60 * 60 * 24);
                        if (daysDiff > parseInt(dateRangeFilter)) {
                            includeRow = false;
                        }
                    }
                }
            }

            return includeRow;
        });

        // Update the filtered data count
        const filterInfoElement = document.getElementById('filterInfo');
        if (filterInfoElement) {
            filterInfoElement.textContent = `Showing ${filteredData.length} of ${csvData.length} assessments`;
        } else {
            const infoElement = document.createElement('div');
            infoElement.id = 'filterInfo';
            infoElement.className = 'alert alert-info mt-3';
            infoElement.textContent = `Showing ${filteredData.length} of ${csvData.length} assessments`;

            const filterContainer = document.querySelector('.card.shadow-sm.mb-4');
            if (filterContainer) {
                filterContainer.after(infoElement);
            }
        }

        // Re-process data with filtered dataset
        processFilteredData(filteredData);

        console.log('Filters applied');
    } catch (error) {
        console.error('Error in applyDataFilters:', error);
    }
}

// Reset data filters
function resetDataFilters() {
    try {
        console.log('Resetting data filters...');

        // Reset filter selections
        document.getElementById('completionFilter').value = 'all';
        document.getElementById('dateRangeFilter').value = 'all';

        // Reset custom date inputs
        document.getElementById('startDateFilter').value = '';
        document.getElementById('endDateFilter').value = '';

        // Hide custom date range inputs
        document.getElementById('customDateRange').style.display = 'none';

        // Reset protocol filter if it exists
        const protocolFilter = document.getElementById('protocolFilter');
        if (protocolFilter) {
            protocolFilter.value = 'all';
        }

        // Remove filter info
        const filterInfoElement = document.getElementById('filterInfo');
        if (filterInfoElement) {
            filterInfoElement.remove();
        }

        // Re-process data with original dataset
        processData();

        console.log('Filters reset');
    } catch (error) {
        console.error('Error in resetDataFilters:', error);
    }
}

// Process filtered data
function processFilteredData(filteredData) {
    try {
        console.log(`Processing ${filteredData.length} filtered records...`);

        // Store original data
        const originalData = csvData;

        // Temporarily replace data with filtered data
        csvData = filteredData;

        // Re-run all analyses
        calculateUniquePatients();
        analyzeAssessmentMethods();
        calculateCompletionTime();
        analyzeMostUsedProtocols();
        analyzePractitionerActivity();
        analyzePractitionerPatients();
        analyzeAgeDistribution();
        calculateCompletionRate();
        analyzeMonthlyTrends();
        analyzeScoreDistribution();
        analyzePatientReassessments();
        analyzeQuestionnaires();

        // Always run time-based analyses
        analyzeTimeOfDay();
        analyzeDayOfWeek();
        analyzeWeeklyTrends();

        // Run other advanced analytics if they exist
        if (typeof analyzePatientEngagement === 'function') analyzePatientEngagement();
        if (typeof analyzeCompletionTimeByProtocol === 'function') analyzeCompletionTimeByProtocol();
        if (typeof analyzeScoreImprovements === 'function') analyzeScoreImprovements();

        // Restore original data
        csvData = originalData;

        console.log('Filtered data processing complete');
    } catch (error) {
        console.error('Error in processFilteredData:', error);
    }
}
