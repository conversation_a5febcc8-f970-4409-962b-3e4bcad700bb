/**
 * CSV Analytics Dashboard - Main Application
 *
 * This file contains the main application logic for the CSV Analytics Dashboard.
 * It handles CSV file parsing, data analysis, and report generation.
 */

// Global variables
let csvData = [];
let jsonData = null;

// Creyos brand colors
const creyosColors = {
    iconBlue: '#12C0DB',
    teal: '#4393A7',
    accessibleTeal: '#0E7A95',
    darkBlue: '#3A4661',
    accentBlue: '#BDF2FF',
    accentBlue80: '#CAF5FF',
    accentBlue60: '#D7F7FF',
    accentBlue40: '#E5FAFF'
};

// Get Creyos brand colors for charts
function getCreyosChartColors(count = 8) {
    const colors = [
        creyosColors.iconBlue,
        creyosColors.teal,
        creyosColors.accessibleTeal,
        creyosColors.darkBlue,
        creyosColors.accentBlue,
        creyosColors.accentBlue80,
        creyosColors.accentBlue60,
        creyosColors.accentBlue40
    ];

    // If we need more colors, repeat the pattern
    if (count > colors.length) {
        const repeats = Math.ceil(count / colors.length);
        return Array(repeats).fill(colors).flat().slice(0, count);
    }

    return colors.slice(0, count);
}

// Convert hex color to RGB
function hexToRgb(hex) {
    // Remove the hash if it exists
    hex = hex.replace(/^#/, '');

    // Parse the hex values
    let r, g, b;
    if (hex.length === 3) {
        // For shorthand hex (e.g., #ABC)
        r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
        g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
        b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
    } else {
        // For full hex (e.g., #AABBCC)
        r = parseInt(hex.substring(0, 2), 16);
        g = parseInt(hex.substring(2, 4), 16);
        b = parseInt(hex.substring(4, 6), 16);
    }

    // Return RGB values as a string
    return `${r}, ${g}, ${b}`;
}
// Format hour for chart labels
function formatHour(hour) {
    // Format as 12-hour time with AM/PM
    return hour === 0 ? '12 AM' :
           hour === 12 ? '12 PM' :
           hour < 12 ? `${hour} AM` :
           `${hour - 12} PM`;
}

// DOM elements - will be initialized after DOM is loaded
let uploadForm;
let uploadFormMain;
let csvFileInput;
let csvFileMainInput;
let analysisSection;
let selectAllBtn;
let deselectAllBtn;
let generateReportBtn;
let previewDataBtn;
let insightCheckboxes;
let reportDate;
let clinicNameElement;
let dataRangeElement;
let dragDropArea;
let fileInfo;
let fileName;
let removeFileBtn;

// Initialize DOM elements after the DOM is loaded
function initializeDOMElements() {
    uploadForm = document.getElementById('uploadForm');
    uploadFormMain = document.getElementById('uploadFormMain');
    csvFileInput = document.getElementById('csvFile');
    csvFileMainInput = document.getElementById('csvFileMain');
    analysisSection = document.getElementById('analysisSection');
    selectAllBtn = document.getElementById('selectAllBtn');
    deselectAllBtn = document.getElementById('deselectAllBtn');
    generateReportBtn = document.getElementById('generateReportBtn');
    previewDataBtn = document.getElementById('previewDataBtn');
    insightCheckboxes = document.querySelectorAll('.insight-checkbox');
    reportDate = document.getElementById('reportDate');
    clinicNameElement = document.getElementById('clinicName');
    dataRangeElement = document.getElementById('dataRange');

    // Drag and drop elements
    dragDropArea = document.getElementById('dragDropArea');
    fileInfo = document.getElementById('fileInfo');
    fileName = document.getElementById('fileName');
    removeFileBtn = document.getElementById('removeFile');
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    initializeDOMElements();

    // Set current date in the report if element exists
    if (reportDate) {
        const today = new Date();
        reportDate.textContent = `Report generated on ${today.toLocaleDateString()}`;
    }

    // Event listeners - check if elements exist before adding listeners
    if (uploadForm) {
        uploadForm.addEventListener('submit', handleFileUpload);
    }

    if (uploadFormMain) {
        uploadFormMain.addEventListener('submit', handleFileUpload);
    }

    // Drag and drop event listeners
    if (dragDropArea) {
        // File input change event
        csvFileMainInput.addEventListener('change', handleFileSelection);

        // Drag and drop events
        dragDropArea.addEventListener('dragover', handleDragOver);
        dragDropArea.addEventListener('dragleave', handleDragLeave);
        dragDropArea.addEventListener('drop', handleDrop);

        // Handle click on the drag area (but not on the button or file input)
        dragDropArea.addEventListener('click', function(e) {
            // Don't trigger if clicking on the remove button
            if (e.target.closest('#removeFile')) {
                return;
            }

            // Don't trigger if clicking on the browse button or its children
            if (e.target.closest('#browseFilesBtn')) {
                console.log('Browse button clicked - letting the label handle it');
                return;
            }

            // Don't trigger if clicking on the file input itself
            if (e.target.tagName === 'INPUT') {
                return;
            }

            console.log('Drag area clicked - opening file dialog');
            // Trigger file input click when the drag area is clicked
            // Even if a file is already selected, allow selecting a new one
            csvFileMainInput.click();
        });

        // Remove file button
        if (removeFileBtn) {
            removeFileBtn.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent triggering the dragDropArea click
                resetFileUpload();
            });
        }
    }

    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', selectAllInsights);
    }

    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', deselectAllInsights);
    }

    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', generatePDF);
    }

    if (previewDataBtn) {
        previewDataBtn.addEventListener('click', showDataPreview);
    }

    // Modal close events
    const closeModalBtn = document.querySelector('#dataPreviewModal .btn-close');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeDataPreview);
    }

    document.addEventListener('click', function(event) {
        // Close modal when clicking outside of it
        const dataPreviewModal = document.getElementById('dataPreviewModal');
        const modalOverlay = document.querySelector('.modal-backdrop');
        if ((dataPreviewModal && event.target === dataPreviewModal) ||
            (modalOverlay && event.target === modalOverlay)) {
            closeDataPreview();
        }
    });

    // Add event listeners to all checkboxes
    insightCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateReportPreview);
    });

    // Initialize chart containers
    ChartRegistry.initializeContainers();
    console.log('Application initialized');
});

// Handle drag over event
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();

    // Add active class for visual feedback
    dragDropArea.classList.add('drag-active');
}

// Handle drag leave event
function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();

    // Remove active class
    dragDropArea.classList.remove('drag-active');
}

// Handle drop event
function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();

    // Remove active class
    dragDropArea.classList.remove('drag-active');

    // Get the dropped files
    const files = event.dataTransfer.files;

    if (files.length > 0) {
        // Only process the first file
        const file = files[0];

        // Check if it's a CSV file
        if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
            // Set the file to the file input
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            csvFileMainInput.files = dataTransfer.files;

            // Update UI to show the selected file
            displayFileInfo(file);
        } else {
            alert('Please upload a CSV file.');
        }
    }
}

// Handle file selection from the file input
function handleFileSelection(event) {
    console.log('File input change event triggered');

    const file = event.target.files[0];

    if (file) {
        // Update UI to show the selected file
        displayFileInfo(file);

        console.log('File selected:', file.name);
    } else {
        // If no file is selected (user canceled), reset the UI
        resetFileUpload();
        console.log('File selection canceled or no file selected');
    }
}

// Display file information
function displayFileInfo(file) {
    if (file) {
        // Show file info and hide drag message
        fileInfo.classList.remove('d-none');
        document.querySelector('.drag-drop-message').classList.add('d-none');

        // Update file name display
        fileName.textContent = file.name;
    }
}

// Reset file upload
function resetFileUpload() {
    // Clear the file input
    csvFileMainInput.value = '';

    // Hide file info and show drag message
    fileInfo.classList.add('d-none');
    document.querySelector('.drag-drop-message').classList.remove('d-none');
}

// Handle file upload
function handleFileUpload(event) {
    event.preventDefault();

    // Determine which form was submitted
    let file;
    if (event.target.id === 'uploadFormMain') {
        file = csvFileMainInput.files[0];

        // Ensure the UI is updated to show the selected file
        if (file && dragDropArea) {
            displayFileInfo(file);
        }
    } else {
        file = csvFileInput.files[0];
    }

    if (!file) {
        alert('Please select a CSV file to upload.');
        return;
    }

    console.log('Processing file:', file.name);

    // Show the side menu insights selection
    const insightsSelection = document.getElementById('insightsSelection');
    if (insightsSelection) {
        insightsSelection.style.display = 'block';
    }

    // Parse CSV file
    Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        encoding: "UTF-8",
        delimiter: ",",  // Comma delimiter
        quoteChar: '"',   // Quote character
        comments: false,  // No comments in the CSV
        dynamicTyping: false, // Don't convert types automatically
        complete: function(results) {
            console.log('CSV parsing complete:', results);

            // Store the parsed data
            csvData = results.data;

            // Convert CSV data to JSON
            convertCsvToJson(csvData);
            console.log('Converted to JSON:', jsonData);

            // Show analysis section
            analysisSection.style.display = 'block';

            // Set clinic name in the report
            if (jsonData && jsonData.length > 0 && jsonData[0]['Clinic Name']) {
                clinicNameElement.textContent = jsonData[0]['Clinic Name'];
            }

            // Initialize chart containers
            const containerCount = ChartRegistry.initializeContainers();
            console.log(`Initialized ${containerCount} chart containers`);

            // Process data with a small delay to ensure DOM is ready
            setTimeout(() => {
                processData();
                updateReportPreview();

                // Scroll to analysis section
                analysisSection.scrollIntoView({ behavior: 'smooth' });
            }, 300);
        },
        error: function(error) {
            console.error('Error parsing CSV:', error);
            alert('Error parsing CSV file. Please check the file format.');
        }
    });
}

// Show data preview in modal
function showDataPreview() {
    console.log('Showing data preview...');

    if (!jsonData || jsonData.length === 0) {
        alert('No data available to preview.');
        return;
    }

    // Get DOM elements
    const dataPreviewModal = document.getElementById('dataPreviewModal');
    const modalOverlay = document.getElementById('modalOverlay');
    const previewTableHead = document.getElementById('previewTableHead');
    const previewTableBody = document.getElementById('previewTableBody');

    // Check if elements exist
    if (!dataPreviewModal || !previewTableHead || !previewTableBody) {
        console.error('Modal elements not found');
        alert('Error showing preview. Please try again.');
        return;
    }

    // Get the first 15 rows (or all if less than 15)
    const previewRows = jsonData.slice(0, 15);

    // Get all column headers from the first row
    const headers = Object.keys(previewRows[0]);

    // Create table header
    let headerHTML = '<tr>';
    headers.forEach(header => {
        headerHTML += `<th>${header}</th>`;
    });
    headerHTML += '</tr>';
    previewTableHead.innerHTML = headerHTML;

    // Create table rows
    let rowsHTML = '';
    previewRows.forEach((row, index) => {
        rowsHTML += '<tr>';
        headers.forEach(header => {
            rowsHTML += `<td>${row[header] || ''}</td>`;
        });
        rowsHTML += '</tr>';
    });
    previewTableBody.innerHTML = rowsHTML;

    // Show the modal using Bootstrap's modal API if available
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const modal = new bootstrap.Modal(dataPreviewModal);
        modal.show();
    } else {
        // Fallback to manual showing
        dataPreviewModal.classList.add('show');
        dataPreviewModal.style.display = 'block';
        if (modalOverlay) {
            modalOverlay.style.display = 'block';
        }
        document.body.classList.add('modal-open');
    }
}

// Close data preview modal
function closeDataPreview() {
    const dataPreviewModal = document.getElementById('dataPreviewModal');
    const modalOverlay = document.getElementById('modalOverlay');

    if (!dataPreviewModal) return;

    // Hide the modal using Bootstrap's modal API if available
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const modalInstance = bootstrap.Modal.getInstance(dataPreviewModal);
        if (modalInstance) {
            modalInstance.hide();
        }
    } else {
        // Fallback to manual hiding
        dataPreviewModal.classList.remove('show');
        dataPreviewModal.style.display = 'none';
        if (modalOverlay) {
            modalOverlay.style.display = 'none';
        }
        document.body.classList.remove('modal-open');
    }
}

// Convert CSV data to JSON
function convertCsvToJson(csvData) {
    console.log('Converting CSV to JSON...');

    try {
        // Deep clone the CSV data to avoid reference issues
        jsonData = JSON.parse(JSON.stringify(csvData));

        // Process numeric values and dates
        jsonData.forEach(row => {
            Object.keys(row).forEach(key => {
                // Try to convert numeric strings to numbers
                if (row[key] && !isNaN(row[key]) && row[key].trim() !== '') {
                    const numValue = parseFloat(row[key]);
                    if (!isNaN(numValue)) {
                        row[key] = numValue;
                    }
                }

                // Try to parse dates (YYYY-MM-DD format)
                if (row[key] && typeof row[key] === 'string' &&
                    row[key].match(/^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2})?/)) {
                    try {
                        const dateObj = new Date(row[key]);
                        if (!isNaN(dateObj.getTime())) {
                            // Store as ISO string for consistency
                            row[key + '_date'] = dateObj.toISOString();
                        }
                    } catch (e) {
                        // If date parsing fails, keep the original string
                        console.warn(`Failed to parse date: ${row[key]}`, e);
                    }
                }
            });
        });

        console.log('CSV to JSON conversion complete');
    } catch (error) {
        console.error('Error converting CSV to JSON:', error);
        // Fallback to original CSV data
        jsonData = csvData;
    }
}



// Calculate date range of the data
function calculateDateRange(data) {
    console.log('Calculating date range...');

    try {
        // Extract dates from the data
        let dates = data
            .filter(row => row['Time administered at'])
            .map(row => new Date(row['Time administered at']))
            .filter(date => !isNaN(date.getTime()));

        if (dates.length === 0) {
            console.log('No valid dates found in data');
            return { formattedRange: "No date data available" };
        }

        // Find min and max dates
        const minDate = new Date(Math.min.apply(null, dates));
        const maxDate = new Date(Math.max.apply(null, dates));

        console.log(`Date range: ${minDate.toLocaleDateString()} - ${maxDate.toLocaleDateString()}`);

        return {
            startDate: minDate,
            endDate: maxDate,
            formattedRange: `${minDate.toLocaleDateString()} - ${maxDate.toLocaleDateString()}`
        };
    } catch (error) {
        console.error('Error calculating date range:', error);
        return { formattedRange: "Error calculating date range" };
    }
}

// Process data to extract insights
function processData() {
    console.log('Processing data...');

    // Use jsonData if available, otherwise fall back to csvData
    const dataToProcess = jsonData || csvData;

    if (!dataToProcess || dataToProcess.length === 0) {
        console.warn('No data to process');
        return;
    }

    console.log(`Processing ${dataToProcess.length} records...`);

    // Calculate date range and display it
    const dateRange = calculateDateRange(dataToProcess);
    if (dataRangeElement && dateRange) {
        dataRangeElement.textContent = `Data range: ${dateRange.formattedRange}`;
    }

    // This function will process the data and extract various insights
    calculateUniquePatients();
    analyzeAssessmentMethods();
    calculateCompletionTime();
    analyzeMostUsedProtocols();
    analyzePractitionerActivity();
    analyzePractitionerPatients();
    analyzeAgeDistribution();
    calculateCompletionRate();
    analyzeMonthlyTrends();
    analyzeScoreDistribution();
    analyzePatientReassessments();
    analyzeQuestionnaires();

    // Time-based analysis functions
    analyzeTimeOfDay();
    analyzeDayOfWeek();
    analyzeWeeklyTrends();

    // Create completion filter
    createCompletionFilter();

    // Initialize collapsible info boxes
    initializeInfoBoxes();

    console.log('Data processing complete');
}

// Calculate unique patients
function calculateUniquePatients() {
    try {
        console.log('Calculating unique patients...');
        const uniquePatients = new Set();

        // Use jsonData if available, otherwise fall back to csvData
        const dataToProcess = jsonData || csvData;

        dataToProcess.forEach(row => {
            if (row['Patient ID']) {
                uniquePatients.add(row['Patient ID']);
            }
        });

        const count = uniquePatients.size;
        const countElement = document.getElementById('uniquePatientsCount');
        if (countElement) {
            countElement.textContent = count;
        }

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'uniquePatientsChart',
            'doughnut',
            {
                labels: ['Unique Patients', 'Total Assessments'],
                datasets: [{
                    data: [count, dataToProcess.length],
                    backgroundColor: [creyosColors.iconBlue, creyosColors.teal], // Creyos Icon Blue, Creyos Teal
                    borderWidth: 1
                }]
            },
            {
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Unique Patients vs Total Assessments'
                    }
                }
            }
        );

        console.log('Unique patients calculation complete');
    } catch (error) {
        console.error('Error in calculateUniquePatients:', error);
    }
}

// Analyze assessment methods
function analyzeAssessmentMethods() {
    try {
        console.log('Analyzing assessment methods...');
        const methods = {};

        // Use jsonData if available, otherwise fall back to csvData
        const dataToProcess = jsonData || csvData;

        dataToProcess.forEach(row => {
            const method = row['Administration Method'];
            if (method) {
                methods[method] = (methods[method] || 0) + 1;
            }
        });

        const labels = Object.keys(methods);
        const data = Object.values(methods);
        const total = data.reduce((sum, value) => sum + value, 0);

        // Update table
        const tableBody = document.getElementById('assessmentMethodsTable');
        if (tableBody) {
            tableBody.innerHTML = '';

            labels.forEach((method, index) => {
                const count = data[index];
                const percentage = ((count / total) * 100).toFixed(1);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${method}</td>
                    <td>${count}</td>
                    <td>${percentage}%</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'assessmentMethodsChart',
            'pie',
            {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: getCreyosChartColors(5),
                    borderWidth: 1
                }]
            },
            {
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    title: {
                        display: true,
                        text: 'Assessment Methods Distribution'
                    }
                }
            }
        );

        console.log('Assessment methods analysis complete');
    } catch (error) {
        console.error('Error in analyzeAssessmentMethods:', error);
    }
}

// Calculate average completion time
function calculateCompletionTime() {
    try {
        console.log('Calculating completion time...');
        let totalMinutes = 0;
        let count = 0;
        const completionTimes = [];

        csvData.forEach(row => {
            if (row['Session Start'] && row['Session Completed']) {
                const startTime = new Date(row['Session Start']);
                const endTime = new Date(row['Session Completed']);

                if (!isNaN(startTime) && !isNaN(endTime)) {
                    const diffMinutes = (endTime - startTime) / (1000 * 60);

                    if (diffMinutes > 0 && diffMinutes < 120) { // Filter out unreasonable times
                        totalMinutes += diffMinutes;
                        count++;
                        completionTimes.push(diffMinutes);
                    }
                }
            }
        });

        const avgTime = count > 0 ? (totalMinutes / count).toFixed(1) : 0;
        const timeElement = document.getElementById('avgCompletionTime');
        if (timeElement) {
            timeElement.textContent = avgTime;
        }

        // Group completion times into bins
        const bins = [0, 5, 10, 15, 20, 25, 30, 45, 60, 90, 120];
        const binCounts = Array(bins.length - 1).fill(0);

        completionTimes.forEach(time => {
            for (let i = 0; i < bins.length - 1; i++) {
                if (time >= bins[i] && time < bins[i + 1]) {
                    binCounts[i]++;
                    break;
                }
            }
        });

        const binLabels = bins.slice(0, -1).map((bin, i) => `${bin}-${bins[i+1]} min`);

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'completionTimeChart',
            'bar',
            {
                labels: binLabels,
                datasets: [{
                    label: 'Number of Assessments',
                    data: binCounts,
                    backgroundColor: creyosColors.accessibleTeal,
                    borderWidth: 1
                }]
            },
            {
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Assessments'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Completion Time (minutes)'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Assessment Completion Time Distribution'
                    }
                }
            }
        );

        console.log('Completion time calculation complete');
    } catch (error) {
        console.error('Error in calculateCompletionTime:', error);
    }
}

// Analyze most used protocols
function analyzeMostUsedProtocols() {
    try {
        console.log('Analyzing most used protocols...');
        const protocols = {};

        csvData.forEach(row => {
            if (row['Protocol Name']) {
                const protocolName = row['Protocol Name'];

                // Check if protocol has a timestamp (starts with numbers)
                const hasTimestamp = /^\d+\s+/.test(protocolName);

                if (hasTimestamp) {
                    // Remove timestamp but keep the full protocol name
                    const fullProtocol = protocolName.replace(/^\d+\s+/, '').trim();
                    protocols[fullProtocol] = (protocols[fullProtocol] || 0) + 1;
                } else {
                    // For protocols without timestamps, use the entire field as is
                    protocols[protocolName] = (protocols[protocolName] || 0) + 1;
                }
            }
        });

        // Sort protocols by usage
        const sortedProtocols = Object.entries(protocols)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10); // Get top 10

        // Store original full protocol names for tooltips and PDF
        const originalLabels = sortedProtocols.map(item => item[0]);

        // Create shortened labels with ellipsis for display
        const labels = sortedProtocols.map(item => {
            const name = item[0];
            // If name is longer than 30 characters, truncate and add ellipsis
            return name.length > 30 ? name.substring(0, 27) + '...' : name;
        });
        const data = sortedProtocols.map(item => item[1]);

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'protocolsChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Usage Count',
                    data: data,
                    backgroundColor: function(context) {
                        const label = context.chart.data.labels[context.dataIndex];
                        // Special colors for categorized protocols
                        if (label === 'ADHD') {
                            return creyosColors.iconBlue; // Creyos Icon Blue for ADHD
                        } else if (label === 'MCI') {
                            return creyosColors.accentBlue; // Creyos Accent Blue for MCI
                        }
                        // Alternate colors for other protocols using only Creyos brand colors
                        return context.dataIndex % 2 === 0 ? creyosColors.teal : creyosColors.darkBlue; // Alternate Teal and Dark Blue
                    },
                    borderWidth: 1
                }]
            },
            {
                indexAxis: 'y',
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Assessments'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Most Used Protocols'
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                // Use the original full protocol name for tooltip title
                                const index = context[0].dataIndex;
                                return originalLabels[index];
                            },
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        );

        console.log('Protocol analysis complete');
    } catch (error) {
        console.error('Error in analyzeMostUsedProtocols:', error);
    }
}

// Analyze practitioner activity
function analyzePractitionerActivity() {
    try {
        console.log('Analyzing practitioner activity...');
        const practitioners = {};

        csvData.forEach(row => {
            const practitioner = row['Provider/Practitioner'];
            if (practitioner) {
                practitioners[practitioner] = (practitioners[practitioner] || 0) + 1;
            }
        });

        // Sort practitioners by activity
        const sortedPractitioners = Object.entries(practitioners)
            .sort((a, b) => b[1] - a[1]);

        const labels = sortedPractitioners.map(item => item[0]);
        const data = sortedPractitioners.map(item => item[1]);

        // Update table
        const tableBody = document.getElementById('practitionerActivityTable');
        if (tableBody) {
            tableBody.innerHTML = '';

            sortedPractitioners.forEach(([practitioner, count]) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${practitioner}</td>
                    <td>${count}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'practitionerActivityChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Assessments',
                    data: data,
                    backgroundColor: '#6610f2',
                    borderWidth: 1
                }]
            },
            {
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Assessments'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Practitioner Activity'
                    }
                }
            }
        );

        console.log('Practitioner activity analysis complete');
    } catch (error) {
        console.error('Error in analyzePractitionerActivity:', error);
    }
}

// Analyze unique patients per practitioner
function analyzePractitionerPatients() {
    try {
        console.log('Analyzing patients per practitioner...');

        // Check if the section exists in the DOM
        const sectionElement = document.getElementById('practitionerPatientsSection');
        if (!sectionElement) {
            console.log('Practitioner patients section not found in DOM');
            return;
        }

        // Map to store practitioner-patient relationships
        const practitionerPatients = {};

        // Process data to find unique patients per practitioner
        csvData.forEach(row => {
            const practitioner = row['Provider/Practitioner'];
            const patientId = row['Patient ID'];

            if (practitioner && patientId) {
                if (!practitionerPatients[practitioner]) {
                    practitionerPatients[practitioner] = new Set();
                }
                practitionerPatients[practitioner].add(patientId);
            }
        });

        // Convert to array of [practitioner, patientCount] and sort by patient count
        const practitionerData = Object.entries(practitionerPatients)
            .map(([practitioner, patients]) => [practitioner, patients.size])
            .sort((a, b) => b[1] - a[1]);

        // Extract labels and data for chart
        const labels = practitionerData.map(item => item[0]);
        const data = practitionerData.map(item => item[1]);

        // Update table
        const tableBody = document.getElementById('practitionerPatientsTable');
        if (tableBody) {
            tableBody.innerHTML = '';

            practitionerData.forEach(([practitioner, count]) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${practitioner}</td>
                    <td>${count}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Create chart
        ChartRegistry.createChart(
            'practitionerPatientsChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Unique Patients',
                    data: data,
                    backgroundColor: creyosColors.accentBlue,
                    borderWidth: 1
                }]
            },
            {
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Unique Patients'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Unique Patients per Practitioner'
                    }
                }
            }
        );

        console.log('Practitioner patients analysis complete');
    } catch (error) {
        console.error('Error in analyzePractitionerPatients:', error);
    }
}

// Analyze age distribution
function analyzeAgeDistribution() {
    try {
        console.log('Analyzing age distribution...');
        const ageGroups = {
            '0-5': 0,
            '6-7': 0,
            '8-9': 0,
            '10-12': 0,
            '13-15': 0,
            '16-17': 0,
            '18-24': 0,
            '25-34': 0,
            '35-44': 0,
            '45-54': 0,
            '55-64': 0,
            '65-74': 0,
            '75+': 0
        };

        csvData.forEach(row => {
            if (row['Date of Birth']) {
                const birthDate = new Date(row['Date of Birth']);
                if (!isNaN(birthDate)) {
                    const today = new Date();
                    const age = today.getFullYear() - birthDate.getFullYear();

                    // Adjust age if birthday hasn't occurred yet this year
                    const monthDiff = today.getMonth() - birthDate.getMonth();
                    const dayDiff = today.getDate() - birthDate.getDate();
                    const adjustedAge = (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) ? age - 1 : age;

                    // More detailed age groups for under 18
                    if (adjustedAge < 6) ageGroups['0-5']++;
                    else if (adjustedAge < 8) ageGroups['6-7']++;
                    else if (adjustedAge < 10) ageGroups['8-9']++;
                    else if (adjustedAge < 13) ageGroups['10-12']++;
                    else if (adjustedAge < 16) ageGroups['13-15']++;
                    else if (adjustedAge < 18) ageGroups['16-17']++;
                    else if (adjustedAge < 25) ageGroups['18-24']++;
                    else if (adjustedAge < 35) ageGroups['25-34']++;
                    else if (adjustedAge < 45) ageGroups['35-44']++;
                    else if (adjustedAge < 55) ageGroups['45-54']++;
                    else if (adjustedAge < 65) ageGroups['55-64']++;
                    else if (adjustedAge < 75) ageGroups['65-74']++;
                    else ageGroups['75+']++;
                }
            }
        });

        const labels = Object.keys(ageGroups);
        const data = Object.values(ageGroups);

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'ageDistributionChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Patients',
                    data: data,
                    backgroundColor: creyosColors.teal,
                    borderWidth: 1
                }]
            },
            {
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Patients'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Age Group'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Patient Age Distribution'
                    }
                }
            }
        );

        console.log('Age distribution analysis complete');
    } catch (error) {
        console.error('Error in analyzeAgeDistribution:', error);
    }
}

// Calculate assessment completion rate
function calculateCompletionRate() {
    try {
        console.log('Calculating completion rate...');
        let completed = 0;
        let total = 0;

        csvData.forEach(row => {
            if (row['Assessments Completed']) {
                const parts = row['Assessments Completed'].split('/');
                if (parts.length === 2) {
                    const [done, all] = parts.map(Number);
                    if (!isNaN(done) && !isNaN(all)) {
                        // Count as completed (1) if:
                        // 1. done >= all (e.g., 1/1, 2/1, 3/1, 4/1)
                        // 2. done/all is a ratio like 3/2, 4/3, 5/4, etc.
                        if (done >= all || (done >= 3 && all >= 2 && done > all - 1)) {
                            completed += 1;
                            total += 1;
                        } else {
                            // For other ratios, count normally
                            completed += done;
                            total += all;
                        }
                    }
                }
            }
        });

        const completionRate = total > 0 ? ((completed / total) * 100).toFixed(1) : 0;
        const rateElement = document.getElementById('completionRate');
        if (rateElement) {
            rateElement.textContent = `${completionRate}%`;
        }

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'assessmentCompletionChart',
            'doughnut',
            {
                labels: ['Completed', 'Not Completed'],
                datasets: [{
                    data: [completed, total - completed],
                    backgroundColor: [creyosColors.iconBlue, creyosColors.darkBlue],
                    borderWidth: 1
                }]
            },
            {
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Assessment Completion Rate'
                    }
                }
            }
        );

        console.log('Completion rate calculation complete');
    } catch (error) {
        console.error('Error in calculateCompletionRate:', error);
    }
}

// Analyze monthly trends
function analyzeMonthlyTrends() {
    try {
        console.log('Analyzing monthly trends...');
        const monthlyData = {};

        csvData.forEach(row => {
            if (row['Time administered at']) {
                const date = new Date(row['Time administered at']);
                if (!isNaN(date)) {
                    const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                    monthlyData[monthYear] = (monthlyData[monthYear] || 0) + 1;
                }
            }
        });

        // Set chart title
        const chartTitle = 'Monthly Assessment Trends';

        // Sort months chronologically
        const sortedMonths = Object.keys(monthlyData).sort();
        const data = sortedMonths.map(month => monthlyData[month]);

        // Format month labels
        const labels = sortedMonths.map(month => {
            const [year, monthNum] = month.split('-');
            const date = new Date(year, monthNum - 1, 1);
            return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        });

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'monthlyTrendsChart',
            'line',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Assessments',
                    data: data,
                    borderColor: '#0dcaf0',
                    backgroundColor: 'rgba(13, 202, 240, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            {
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Assessments'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: chartTitle
                    }
                }
            }
        );

        console.log('Monthly trends analysis complete');
    } catch (error) {
        console.error('Error in analyzeMonthlyTrends:', error);
    }
}

// Analyze score distribution
function analyzeScoreDistribution() {
    try {
        console.log('Analyzing score distribution...');
        // Find cognitive test scores (looking for columns with "standard" in the name)
        const scoreColumns = Object.keys(csvData[0] || {}).filter(key => key.includes('standard'));

        if (scoreColumns.length === 0) {
            const section = document.getElementById('scoreDistributionSection');
            if (section) {
                section.style.display = 'none';
            }
            console.log('No score columns found');
            return;
        }

        // Group scores into categories
        const scoreRanges = {
            'Very Low (<70)': 0,
            'Low (70-85)': 0,
            'Low Average (85-100)': 0,
            'Average (100-115)': 0,
            'High Average (115-130)': 0,
            'High (>130)': 0
        };

        let totalScores = 0;

        csvData.forEach(row => {
            scoreColumns.forEach(column => {
                const score = parseFloat(row[column]);
                if (!isNaN(score)) {
                    totalScores++;

                    if (score < 70) scoreRanges['Very Low (<70)']++;
                    else if (score < 85) scoreRanges['Low (70-85)']++;
                    else if (score < 100) scoreRanges['Low Average (85-100)']++;
                    else if (score < 115) scoreRanges['Average (100-115)']++;
                    else if (score < 130) scoreRanges['High Average (115-130)']++;
                    else scoreRanges['High (>130)']++;
                }
            });
        });

        const labels = Object.keys(scoreRanges);
        const data = Object.values(scoreRanges);

        // Create chart using ChartRegistry
        ChartRegistry.createChart(
            'scoreDistributionChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Scores',
                    data: data,
                    backgroundColor: creyosColors.darkBlue,
                    borderWidth: 1
                }]
            },
            {
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Scores'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Score Range'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Cognitive Test Score Distribution'
                    }
                }
            }
        );

        console.log('Score distribution analysis complete');
    } catch (error) {
        console.error('Error in analyzeScoreDistribution:', error);
    }
}

// Select all insights
function selectAllInsights() {
    insightCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateReportPreview();
}

// Deselect all insights
function deselectAllInsights() {
    insightCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateReportPreview();
}

// Update report preview based on selected insights
function updateReportPreview() {
    // Show/hide sections based on checkbox state
    insightCheckboxes.forEach(checkbox => {
        const sectionId = checkbox.id.replace('Check', 'Section');
        const section = document.getElementById(sectionId);

        if (section) {
            section.style.display = checkbox.checked ? 'block' : 'none';
        }
    });
}

// Create a new chart with data labels for PDF export
function createPdfChartFrom(originalChart, containerId) {
    if (!originalChart) {
        console.warn(`No chart instance found for ${containerId}`);
        return null;
    }

    // Get the container and create a new canvas
    const container = document.getElementById(containerId);
    if (!container) {
        console.warn(`Container not found: ${containerId}`);
        return null;
    }

    // Create a new canvas for the PDF chart
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 400;

    // Clear the container and add the new canvas
    container.innerHTML = '';
    container.appendChild(canvas);
    container.style.backgroundColor = 'white';
    container.style.padding = '10px';
    container.style.marginBottom = '20px';
    container.style.border = '1px solid #dee2e6';
    container.style.borderRadius = '0.5rem';

    // Get the 2D context
    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.warn(`Could not get 2D context for ${containerId}`);
        return null;
    }

    try {
        // Clone the original chart configuration
        const chartConfig = JSON.parse(JSON.stringify(originalChart.config));

        // Add data labels configuration based on chart type
        const chartType = chartConfig.type;

        // Set up data labels plugin options
        chartConfig.options = chartConfig.options || {};
        chartConfig.options.plugins = chartConfig.options.plugins || {};

        if (chartType === 'bar' || chartType === 'line') {
            // For bar and line charts
            chartConfig.options.plugins.datalabels = {
                display: true,
                color: '#000000',
                backgroundColor: 'rgba(255, 255, 255, 0.75)',
                borderRadius: 4,
                padding: 4,
                font: {
                    weight: 'bold',
                    size: 14
                },
                formatter: (value) => {
                    return value.toLocaleString();
                },
                align: 'end',
                anchor: 'end'
            };
        } else if (chartType === 'pie' || chartType === 'doughnut') {
            // For pie and doughnut charts
            chartConfig.options.plugins.datalabels = {
                display: true,
                color: '#ffffff',
                backgroundColor: `rgba(${hexToRgb(creyosColors.darkBlue)}, 0.7)`, // Creyos Dark Blue with transparency
                borderRadius: 4,
                padding: 4,
                font: {
                    weight: 'bold',
                    size: 14
                },
                formatter: (value, context) => {
                    const sum = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = Math.round((value / sum) * 100) + '%';
                    return percentage;
                }
            };
        }

        // Create a new chart with the modified configuration
        return new Chart(ctx, chartConfig);
    } catch (error) {
        console.error(`Error creating PDF chart for ${containerId}:`, error);
        return null;
    }
}

// Generate PDF report
async function generatePDF() {
    console.log('Starting PDF generation...');

    // Show loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-indicator';
    loadingIndicator.style.position = 'fixed';
    loadingIndicator.style.top = '0';
    loadingIndicator.style.left = '0';
    loadingIndicator.style.width = '100%';
    loadingIndicator.style.height = '100%';
    loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
    loadingIndicator.style.display = 'flex';
    loadingIndicator.style.flexDirection = 'column';
    loadingIndicator.style.alignItems = 'center';
    loadingIndicator.style.justifyContent = 'center';
    loadingIndicator.style.zIndex = '9999';
    loadingIndicator.innerHTML = `
        <div class="spinner-border text-light" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-light">Generating PDF... Please wait.</p>
    `;
    document.body.appendChild(loadingIndicator);

    try {
        // Create a new jsPDF instance
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pageWidth = 210; // A4 width in mm
        const pageHeight = 297; // A4 height in mm
        const margin = 15; // margin in mm
        const contentWidth = pageWidth - (margin * 2);
        const today = new Date();

        // Get clinic name for use throughout the function
        const clinicName = clinicNameElement.textContent || 'Clinic Report';

        // Function to add header with logo
        const addHeader = (isFirstPage = false) => {
            // Add Creyos logo
            const logoImg = new Image();
            logoImg.src = 'images/CreyosLogo.png';

            // Function to maintain aspect ratio
            const maintainAspectRatio = (img, targetWidth) => {
                const aspectRatio = img.width / img.height;
                const width = targetWidth;
                const height = width / aspectRatio;
                return { width, height };
            };

            // Wait for image to load to get dimensions
            return new Promise((resolve) => {
                logoImg.onload = function() {
                    if (isFirstPage) {
                        // Add logo centered at the top
                        const targetWidth = 40; // mm
                        const { width, height } = maintainAspectRatio(logoImg, targetWidth);

                        pdf.addImage(
                            logoImg,
                            'PNG',
                            (pageWidth / 2) - (width / 2),
                            margin,
                            width,
                            height
                        );

                        // Add title
                        pdf.setFontSize(18);
                        pdf.setFont('helvetica', 'bold');
                        pdf.setTextColor(58, 70, 97); // Dark Blue #3A4661
                        pdf.text('Aggregate Report Analysis', pageWidth / 2, margin + height + 10, { align: 'center' });

                        // Add report date
                        const reportDateElement = document.getElementById('reportDate');
                        const dateText = reportDateElement ? reportDateElement.textContent : `Report generated on ${today.toLocaleDateString()}`;
                        pdf.setFontSize(10);
                        pdf.setFont('helvetica', 'normal');
                        pdf.setTextColor(100, 100, 100);
                        pdf.text(dateText, pageWidth / 2, margin + height + 20, { align: 'center' });

                        // Add data range
                        const dataRangeElement = document.getElementById('dataRange');
                        if (dataRangeElement && dataRangeElement.textContent) {
                            pdf.setFontSize(10);
                            pdf.text(dataRangeElement.textContent, pageWidth / 2, margin + height + 25, { align: 'center' });
                        }

                        // Add clinic name
                        pdf.setFontSize(12);
                        pdf.text(clinicName, pageWidth / 2, margin + height + 35, { align: 'center' });

                        resolve(margin + height + 45); // Return Y position after header
                    } else {
                        // Add small logo and title on subsequent pages with proper aspect ratio
                        const targetWidth = 25; // mm
                        const { width, height } = maintainAspectRatio(logoImg, targetWidth);

                        // Add padding to the logo
                        const logoPadding = 5;
                        pdf.addImage(logoImg, 'PNG', margin + logoPadding, margin, width, height);

                        pdf.setFontSize(10);
                        pdf.setTextColor(58, 70, 97); // Dark Blue #3A4661
                        pdf.text('Aggregate Report Analysis', margin + width + 10, margin + height/2);

                        resolve(margin + Math.max(height, 20) + 5); // Return Y position after header
                    }
                };

                // Handle image loading error
                logoImg.onerror = function() {
                    console.warn('Error loading logo image');
                    if (isFirstPage) {
                        resolve(margin + 65); // Default Y position for first page
                    } else {
                        resolve(margin + 20); // Default Y position for other pages
                    }
                };
            });
        };

        try {
            // Add header to first page
            let yPos = await addHeader(true);

            // Make all info boxes visible for PDF
            document.querySelectorAll('.info-box').forEach(box => {
                box.classList.add('show');
            });

            // Process each section
            const sections = document.querySelectorAll('.report-section');

            // Process each visible section
            for (const section of sections) {
                // Check if section should be included
                const sectionId = section.id;
                const checkboxId = sectionId.replace('Section', 'Check');
                const checkbox = document.getElementById(checkboxId);

                if (!checkbox || !checkbox.checked) {
                    continue; // Skip this section
                }

                // Get section title
                const titleElement = section.querySelector('h4');
                if (!titleElement) continue;

                // Check if we need a new page
                if (yPos > pageHeight - 40) {
                    pdf.addPage();
                    // Add header to new page and wait for it to complete
                    yPos = await addHeader(false);
                }

                // Get section icon and title text
                const iconElement = titleElement.querySelector('i');
                const iconClass = iconElement ? iconElement.className : '';
                const titleText = titleElement.textContent.trim();

                // Determine icon color based on class - using only Creyos brand colors
                let iconColor = creyosColors.iconBlue; // Default to Icon Blue
                if (iconClass.includes('text-creyos-icon-blue')) iconColor = creyosColors.iconBlue;
                if (iconClass.includes('text-creyos-teal')) iconColor = creyosColors.teal;
                if (iconClass.includes('text-creyos-accessible-teal')) iconColor = creyosColors.accessibleTeal;
                if (iconClass.includes('text-creyos-dark-blue')) iconColor = creyosColors.darkBlue;
                if (iconClass.includes('text-creyos-accent-blue')) iconColor = creyosColors.accentBlue;

                // Add section title with icon
                pdf.setFontSize(16);
                pdf.setFont('helvetica', 'bold');
                pdf.setTextColor(58, 70, 97); // Dark Blue #3A4661

                // Draw icon (as a colored square for simplicity)
                pdf.setFillColor(iconColor);
                pdf.rect(margin, yPos - 5, 6, 6, 'F');

                // Add title text
                pdf.text(titleText, margin + 10, yPos);

                // Add colored divider line
                yPos += 5;
                pdf.setDrawColor(iconColor);
                pdf.setLineWidth(0.5);
                pdf.line(margin, yPos, pageWidth - margin, yPos);

                yPos += 10;

                // Find chart in this section
                const chartContainer = section.querySelector('[id$="Chart"]');
                if (chartContainer) {
                    // Get the chart instance
                    const chartId = chartContainer.id;
                    const chart = ChartRegistry.instances[chartId];

                    if (chart) {
                        // Get chart type for use throughout the function
                        const chartType = chart.config.type;
                        // Check if we need a new page
                        if (yPos > pageHeight - 80) {
                            pdf.addPage();
                            // Add header to new page and wait for it to complete
                            yPos = await addHeader(false);
                        }

                        // Get the canvas from the chart
                        const canvas = chartContainer.querySelector('canvas');
                        if (canvas) {
                            // Create a copy of the canvas for PDF export
                            const tempCanvas = document.createElement('canvas');
                            tempCanvas.width = canvas.width;
                            tempCanvas.height = canvas.height;
                            const tempCtx = tempCanvas.getContext('2d');

                            // Copy the original canvas content
                            tempCtx.drawImage(canvas, 0, 0);

                            // Add data labels manually
                            tempCtx.font = 'bold 14px Arial';
                            tempCtx.textAlign = 'center';

                            if (chartType === 'pie' || chartType === 'doughnut') {
                            // For pie/doughnut charts, add percentage and value labels
                            const data = chart.data.datasets[0].data;
                            const labels = chart.data.labels || [];
                            const total = data.reduce((sum, value) => sum + value, 0);
                            const meta = chart.getDatasetMeta(0);

                            for (let i = 0; i < meta.data.length; i++) {
                                const element = meta.data[i];
                                const value = data[i];
                                const percent = Math.round((value / total) * 100) + '%';
                                const label = `${value} (${percent})`;

                                // Calculate position (center of the arc)
                                const centerX = element.x;
                                const centerY = element.y;

                                // Draw background for better visibility
                                tempCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                                const textWidth = tempCtx.measureText(label).width;
                                tempCtx.fillRect(centerX - textWidth/2 - 4, centerY - 10, textWidth + 8, 20);

                                // Draw text
                                tempCtx.fillStyle = 'white';
                                tempCtx.fillText(label, centerX, centerY + 5);

                                // Add label name if space allows (for larger segments)
                                if (percent.replace('%', '') > 15 && labels[i]) {
                                    const labelText = labels[i];
                                    const labelY = centerY + 25;

                                    // Draw background for label name
                                    const labelWidth = tempCtx.measureText(labelText).width;
                                    tempCtx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                                    tempCtx.fillRect(centerX - labelWidth/2 - 4, labelY - 10, labelWidth + 8, 20);

                                    // Draw label name
                                    tempCtx.fillStyle = 'black';
                                    tempCtx.fillText(labelText, centerX, labelY + 5);
                                }
                            }
                        } else if (chartType === 'bar') {
                            // For bar charts, add value labels
                            const datasets = chart.data.datasets;

                            for (let i = 0; i < datasets.length; i++) {
                                const dataset = datasets[i];
                                const meta = chart.getDatasetMeta(i);

                                for (let j = 0; j < meta.data.length; j++) {
                                    const element = meta.data[j];
                                    const value = dataset.data[j].toLocaleString();

                                    // Calculate position (top of the bar)
                                    const x = element.x;
                                    const y = element.y - 10;

                                    // Draw background for better visibility
                                    tempCtx.fillStyle = 'rgba(255, 255, 255, 0.75)';
                                    const textWidth = tempCtx.measureText(value).width;
                                    tempCtx.fillRect(x - textWidth/2 - 4, y - 10, textWidth + 8, 20);

                                    // Draw text
                                    tempCtx.fillStyle = 'black';
                                    tempCtx.fillText(value, x, y + 5);
                                }
                            }
                        } else if (chartType === 'line') {
                            // For line charts, add value labels at data points
                            const datasets = chart.data.datasets;

                            for (let i = 0; i < datasets.length; i++) {
                                const dataset = datasets[i];
                                const meta = chart.getDatasetMeta(i);

                                for (let j = 0; j < meta.data.length; j++) {
                                    const element = meta.data[j];
                                    const value = dataset.data[j].toLocaleString();

                                    // Calculate position (above the point)
                                    const x = element.x;
                                    const y = element.y - 15;

                                    // Draw background for better visibility
                                    tempCtx.fillStyle = 'rgba(255, 255, 255, 0.75)';
                                    const textWidth = tempCtx.measureText(value).width;
                                    tempCtx.fillRect(x - textWidth/2 - 4, y - 10, textWidth + 8, 20);

                                    // Draw text
                                    tempCtx.fillStyle = 'black';
                                    tempCtx.fillText(value, x, y + 5);
                                }
                            }
                        }

                        // Get the image data from the temp canvas
                        const imgData = tempCanvas.toDataURL('image/png');

                        // Check if we need to create a two-column layout
                        const needsTwoColumns = chartType === 'pie' || chartType === 'doughnut';

                        // Calculate dimensions for two-column layout
                        const chartWidth = needsTwoColumns ? contentWidth * 0.55 : contentWidth;
                        const aspectRatio = tempCanvas.width / tempCanvas.height;
                        const chartHeight = chartWidth / aspectRatio;

                        // Add the chart image to the PDF (left side for pie/doughnut charts)
                        pdf.addImage(imgData, 'PNG', margin, yPos, chartWidth, chartHeight);

                        // For pie/doughnut charts, add a data table on the right
                        if (needsTwoColumns) {
                            // Calculate starting position for the table
                            const tableX = margin + chartWidth + 5;
                            const tableY = yPos + 10;

                            // Add table title
                            pdf.setFontSize(12);
                            pdf.setFont('helvetica', 'bold');
                            pdf.setTextColor(58, 70, 97); // Dark Blue
                            pdf.text('Method', tableX, tableY);
                            pdf.text('Count', tableX + 35, tableY);
                            pdf.text('%', tableX + 60, tableY);

                            // Add table data
                            pdf.setFont('helvetica', 'normal');
                            pdf.setTextColor(0, 0, 0);

                            // Chart data is already defined above with proper error handling

                            // Get background colors for the legend with defensive programming
                            const backgroundColors = chart.data?.datasets?.[0]?.backgroundColor || '#12C0DB';

                            // Make sure we have labels defined and handle potential undefined values
                            const chartLabels = chart.data?.labels || [];
                            const chartData = chart.data?.datasets?.[0]?.data || [];
                            const chartTotal = chartData.reduce((sum, value) => sum + (value || 0), 0) || 1; // Avoid division by zero

                            for (let i = 0; i < chartLabels.length; i++) {
                                const rowY = tableY + 15 + (i * 10);
                                const value = chartData[i] || 0;
                                const percent = Math.round((value / chartTotal) * 100) + '%';

                                // Draw color box
                                let color = backgroundColors;
                                if (Array.isArray(backgroundColors)) {
                                    color = backgroundColors[i];
                                } else if (typeof backgroundColors === 'function') {
                                    // Use a default color if we can't determine it - Creyos Icon Blue
                                    color = creyosColors.iconBlue;
                                }

                                pdf.setFillColor(color);
                                pdf.rect(tableX - 5, rowY - 4, 4, 4, 'F');

                                // Draw label (truncate if too long)
                                let label = chartLabels[i];
                                if (label && label.length > 15) {
                                    label = label.substring(0, 12) + '...';
                                }
                                pdf.text(label, tableX, rowY);

                                // Draw count and percentage
                                pdf.text(value.toString(), tableX + 35, rowY);
                                pdf.text(percent, tableX + 60, rowY);
                            }

                            // Use the maximum of chart height and table height
                            const tableHeight = 25 + (chartLabels.length * 10);
                            yPos += Math.max(chartHeight, tableHeight) + 15;
                        } else {
                            // For other chart types, just add the chart
                            yPos += chartHeight + 15;
                        }
                    }
                }
            }

            // Find table in this section
            const table = section.querySelector('table');
            if (table) {
                // Check if we need a new page
                if (yPos > pageHeight - 50) {
                    pdf.addPage();
                    // Add header to new page and wait for it to complete
                    yPos = await addHeader(false);
                }

                // Add table title
                pdf.setFontSize(12);
                pdf.setTextColor(0, 0, 0);
                pdf.text('Data Table:', margin, yPos);
                yPos += 8;

                // Process table rows
                const rows = table.querySelectorAll('tr');
                const headerRow = rows[0];

                if (headerRow) {
                    // Get header cells
                    const headerCells = headerRow.querySelectorAll('th');
                    const headers = Array.from(headerCells).map(cell => cell.textContent.trim());

                    // Calculate column widths
                    const colWidths = [];
                    const totalCols = headers.length;
                    const availableWidth = contentWidth - 10; // 10mm for padding
                    const colWidth = availableWidth / totalCols;

                    for (let i = 0; i < totalCols; i++) {
                        colWidths.push(colWidth);
                    }

                    // Prepare table data
                    const tableData = [];

                    // Add data rows
                    for (let i = 1; i < rows.length; i++) {
                        const cells = rows[i].querySelectorAll('td');
                        const rowData = Array.from(cells).map(cell => cell.textContent.trim());
                        tableData.push(rowData);
                    }

                    // Add table to PDF
                    pdf.autoTable({
                        startY: yPos,
                        head: [headers],
                        body: tableData,
                        margin: { left: margin, right: margin },
                        columnStyles: {
                            0: { cellWidth: colWidths[0] }
                        },
                        headStyles: {
                            fillColor: [67, 97, 238],
                            textColor: [255, 255, 255],
                            fontStyle: 'bold'
                        },
                        alternateRowStyles: {
                            fillColor: [240, 240, 240]
                        },
                        tableLineColor: [200, 200, 200],
                        tableLineWidth: 0.1
                    });

                    // Update yPos after table
                    yPos = pdf.lastAutoTable.finalY + 15;
                }
            }

                // Add info box if present
                const infoBox = section.querySelector('.info-box');
                if (infoBox) {
                    // Check if we need a new page
                    if (yPos > pageHeight - 40) {
                        pdf.addPage();
                        // Add header to new page and wait for it to complete
                        yPos = await addHeader(false);
                    }

                    // Get the info text
                    const infoText = infoBox.textContent.trim();

                    // Draw info box with icon
                    pdf.setFillColor(240, 249, 252); // Light blue background
                    pdf.setDrawColor(18, 192, 219); // Icon Blue border
                    pdf.roundedRect(margin, yPos, contentWidth, 20, 2, 2, 'FD');

                    // Add info icon
                    pdf.setFillColor(18, 192, 219); // Icon Blue
                    pdf.circle(margin + 5, yPos + 10, 2, 'F');

                    // Add info text
                    pdf.setFont('helvetica', 'normal');
                    pdf.setFontSize(9);
                    pdf.setTextColor(58, 70, 97); // Dark Blue

                    // Wrap text to fit in the box
                    const maxWidth = contentWidth - 15;
                    const lines = pdf.splitTextToSize(infoText, maxWidth);

                    // Adjust box height based on number of lines
                    const lineHeight = 5;
                    const boxHeight = Math.max(20, (lines.length * lineHeight) + 10);

                    // Redraw the box with the correct height
                    pdf.setFillColor(240, 249, 252); // Light blue background
                    pdf.setDrawColor(18, 192, 219); // Icon Blue border
                    pdf.roundedRect(margin, yPos, contentWidth, boxHeight, 2, 2, 'FD');

                    // Redraw the icon
                    pdf.setFillColor(18, 192, 219); // Icon Blue
                    pdf.circle(margin + 5, yPos + 10, 2, 'F');

                    // Add the text
                    pdf.text(lines, margin + 10, yPos + 8);

                    // Update position
                    yPos += boxHeight + 10;
                } else {
                    // Add some space after the section
                    yPos += 10;
                }
            }

            // Save the PDF
            const filename = `${clinicName.replace(/[^a-z0-9]/gi, '_')}_Report_${today.toISOString().split('T')[0]}.pdf`;
            pdf.save(filename);

            console.log('PDF saved successfully');
            document.body.removeChild(loadingIndicator);

            // Hide info boxes again
            document.querySelectorAll('.info-box').forEach(box => {
                box.classList.remove('show');
            });

            // Dispatch event to notify that PDF generation is complete
            document.dispatchEvent(new Event('pdfGenerated'));
        } catch (error) {
            console.error('Error in PDF generation:', error);
            alert('Error generating PDF. Please try again.');
            document.body.removeChild(loadingIndicator);
        }
    } catch (error) {
        console.error('Error in PDF generation setup:', error);
        alert('Error generating PDF. Please try again.');
        document.body.removeChild(loadingIndicator);
    }
}

// Analyze time of day distribution
function analyzeTimeOfDay() {
    try {
        console.log('Analyzing time of day distribution...');
        const hourData = {};

        // Initialize hours (0-23)
        for (let i = 0; i < 24; i++) {
            hourData[i] = 0;
        }

        // Count assessments by hour
        csvData.forEach(row => {
            if (row['Time administered at']) {
                const date = new Date(row['Time administered at']);
                if (!isNaN(date)) {
                    const hour = date.getHours();
                    hourData[hour]++;
                }
            }
        });

        // Prepare data for chart
        const labels = Object.keys(hourData).map(hour => {
            const hourNum = parseInt(hour);
            // Format hour
            return formatHour(hourNum);
        });

        const data = Object.values(hourData);

        // Set chart title
        const chartTitle = 'Assessments by Time of Day';

        // Create chart
        ChartRegistry.createChart(
            'timeOfDayChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Assessments',
                    data: data,
                    backgroundColor: `rgba(${hexToRgb(creyosColors.iconBlue)}, 0.7)`, // Creyos Icon Blue with transparency
                    borderColor: creyosColors.iconBlue,
                    borderWidth: 1
                }]
            },
            {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartTitle
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Assessments: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Assessments'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Hour of Day'
                        }
                    }
                }
            }
        );

        console.log('Time of day analysis complete');
    } catch (error) {
        console.error('Error in analyzeTimeOfDay:', error);
    }
}

// Analyze day of week distribution
function analyzeDayOfWeek() {
    try {
        console.log('Analyzing day of week distribution...');
        const dayData = {};

        // Initialize days (0-6, Sunday-Saturday)
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        dayNames.forEach(day => {
            dayData[day] = 0;
        });

        // Count assessments by day of week
        csvData.forEach(row => {
            if (row['Time administered at']) {
                const date = new Date(row['Time administered at']);
                if (!isNaN(date)) {
                    const dayIndex = date.getDay();
                    const dayName = dayNames[dayIndex];
                    dayData[dayName]++;
                }
            }
        });

        // Set chart title
        const chartTitle = 'Assessments by Day of Week';

        // Prepare data for chart
        const labels = dayNames;
        const data = dayNames.map(day => dayData[day]);

        // Create chart
        ChartRegistry.createChart(
            'dayOfWeekChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Assessments',
                    data: data,
                    backgroundColor: 'rgba(32, 201, 151, 0.7)',
                    borderColor: 'rgba(32, 201, 151, 1)',
                    borderWidth: 1
                }]
            },
            {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartTitle
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Assessments: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Assessments'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Day of Week'
                        }
                    }
                }
            }
        );

        console.log('Day of week analysis complete');
    } catch (error) {
        console.error('Error in analyzeDayOfWeek:', error);
    }
}

// Analyze weekly trends
function analyzeWeeklyTrends() {
    try {
        console.log('Analyzing weekly trends...');
        const weeklyData = {};

        // Process data by week
        csvData.forEach(row => {
            if (row['Time administered at']) {
                const date = new Date(row['Time administered at']);
                if (!isNaN(date)) {
                    // Get year and week number
                    const year = date.getFullYear();

                    // Calculate week number (ISO week)
                    // Get first day of year
                    const firstDayOfYear = new Date(year, 0, 1);
                    // Get day of week of first day (0-6)
                    const firstDayOfWeekDay = firstDayOfYear.getDay();
                    // Calculate days since first day of year
                    const daysSinceFirstDay = Math.floor((date - firstDayOfYear) / (24 * 60 * 60 * 1000));
                    // Calculate week number
                    const weekNum = Math.ceil((daysSinceFirstDay + firstDayOfWeekDay + 1) / 7);

                    // Create week key (YYYY-WW)
                    const weekKey = `${year}-W${weekNum.toString().padStart(2, '0')}`;
                    weeklyData[weekKey] = (weeklyData[weekKey] || 0) + 1;
                }
            }
        });

        // Set chart title
        const chartTitle = 'Weekly Assessment Trends';

        // Sort weeks chronologically
        const sortedWeeks = Object.keys(weeklyData).sort();

        // Limit to last 12 weeks for better visualization
        const recentWeeks = sortedWeeks.slice(-12);
        const data = recentWeeks.map(week => weeklyData[week]);

        // Format week labels with date ranges
        const labels = recentWeeks.map(week => {
            const [year, weekNum] = week.split('-W');
            // Get the first day of the week (Monday)
            const firstDayOfWeek = getFirstDayOfISOWeek(parseInt(weekNum), parseInt(year));
            // Get the last day of the week (Sunday)
            const lastDayOfWeek = new Date(firstDayOfWeek);
            lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);

            // Format dates as MM/DD
            const startDateFormatted = `${firstDayOfWeek.getMonth() + 1}/${firstDayOfWeek.getDate()}`;
            const endDateFormatted = `${lastDayOfWeek.getMonth() + 1}/${lastDayOfWeek.getDate()}`;

            return `Week ${weekNum} (${startDateFormatted}-${endDateFormatted})`;
        });

        // Create chart
        ChartRegistry.createChart(
            'weeklyTrendsChart',
            'line',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Assessments',
                    data: data,
                    borderColor: '#20c997',
                    backgroundColor: 'rgba(32, 201, 151, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartTitle
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Assessments: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Assessments'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Week'
                        }
                    }
                }
            }
        );

        console.log('Weekly trends analysis complete');
    } catch (error) {
        console.error('Error in analyzeWeeklyTrends:', error);
    }
}

// Initialize collapsible info boxes
function initializeInfoBoxes() {
    try {
        console.log('Initializing collapsible info boxes...');

        // Add toggle buttons to all section headers
        document.querySelectorAll('.report-section h4').forEach(header => {
            // Check if the header already has a toggle button
            if (header.querySelector('.btn-outline-info')) {
                return; // Skip if button already exists
            }

            // Create info toggle button
            const infoToggle = document.createElement('button');
            infoToggle.className = 'btn btn-sm btn-outline-info ms-2';
            infoToggle.innerHTML = '<i class="fas fa-info-circle"></i>';
            infoToggle.title = 'Toggle information';
            infoToggle.setAttribute('type', 'button');

            // Add click event to toggle the info box
            infoToggle.addEventListener('click', function() {
                const section = this.closest('.report-section');
                const infoBox = section.querySelector('.info-box');
                if (infoBox) {
                    // Use native Bootstrap methods instead of jQuery
                    const bsCollapse = new bootstrap.Collapse(infoBox, {
                        toggle: true
                    });
                }
            });

            // Append the button to the header
            header.appendChild(infoToggle);
        });

        // Make sure info boxes are visible in PDF reports
        document.getElementById('generateReportBtn').addEventListener('click', function() {
            document.querySelectorAll('.info-box').forEach(box => {
                box.classList.add('show');
                box.setAttribute('data-pdf-visible', 'true');
            });
        });

        // Restore info boxes state after PDF generation
        document.addEventListener('pdfGenerated', function() {
            document.querySelectorAll('.info-box[data-pdf-visible="true"]').forEach(box => {
                box.classList.remove('show');
                box.removeAttribute('data-pdf-visible');
            });
        });

        console.log('Info boxes initialized successfully');
    } catch (error) {
        console.error('Error initializing info boxes:', error);
    }
}

// Get the first day (Monday) of an ISO week
function getFirstDayOfISOWeek(week, year) {
    // Create a date for January 4th of the given year (always in week 1 of ISO weeks)
    const jan4 = new Date(year, 0, 4);

    // Get the day of the week for January 4th (0 = Sunday, 1 = Monday, etc.)
    const jan4DayOfWeek = jan4.getDay();

    // Calculate days to the first Monday of the year
    // If January 4th is a Monday (1), then it's already the first day of week 1
    // Otherwise, go back to the previous Monday
    const daysToFirstMonday = jan4DayOfWeek === 0 ? -6 : 1 - jan4DayOfWeek;

    // Calculate the date of the first Monday of week 1
    const firstMondayOfWeek1 = new Date(year, 0, 4 + daysToFirstMonday);

    // Add the required number of weeks (week - 1 because we're starting from week 1)
    const targetDate = new Date(firstMondayOfWeek1);
    targetDate.setDate(firstMondayOfWeek1.getDate() + (week - 1) * 7);

    return targetDate;
}

// Analyze questionnaires
function analyzeQuestionnaires() {
    try {
        console.log('Analyzing questionnaires...');

        // Check if the section exists in the DOM
        const sectionElement = document.getElementById('questionnaireCompletionSection');
        if (!sectionElement) {
            console.log('Questionnaire completion section not found in DOM');
            return;
        }

        // Count questionnaires
        const questionnaires = {};
        let totalQuestionnaires = 0;

        // Define standardized assessment instruments to look for
        const standardizedInstruments = {
            'PHQ-9': ['PHQ-9', 'PHQ9', 'Patient Health Questionnaire-9', 'Patient Health Questionnaire 9'],
            'GAD-7': ['GAD-7', 'GAD7', 'Generalized Anxiety Disorder-7', 'Generalized Anxiety Disorder 7'],
            'ASRS': ['ASRS', 'Adult ADHD Self-Report Scale', 'ADHD Self-Report Scale'],
            'PSS': ['PSS', 'Perceived Stress Scale'],
            'PCL-5': ['PCL-5', 'PCL5', 'PTSD Checklist', 'PTSD Checklist for DSM-5'],
            'VADRS': ['VADRS', 'Vanderbilt ADHD Diagnostic Rating Scale'],
            'DAST-10': ['DAST-10', 'DAST10', 'Drug Abuse Screening Test'],
            'AUDIT': ['AUDIT', 'Alcohol Use Disorders Identification Test'],
            'IADL': ['IADL', 'Instrumental Activities of Daily Living'],
            'MDQ': ['MDQ', 'Mood Disorder Questionnaire'],
            'SWAN': ['SWAN', 'Strengths and Weaknesses of ADHD Symptoms and Normal Behavior Scale']
        };

        // Map to store standardized instrument counts
        const standardizedCounts = {};
        Object.keys(standardizedInstruments).forEach(key => {
            standardizedCounts[key] = 0;
        });

        // Look for questionnaire data in the CSV
        // Common column names for questionnaires
        const possibleQuestionnaireCols = [
            'Questionnaire Name',
            'Questionnaire',
            'Assessment Name',
            'Assessment Type',
            'Test Name',
            'Test Type'
        ];

        // Find which column contains questionnaire data
        let questionnaireCol = null;
        for (const col of possibleQuestionnaireCols) {
            if (csvData.length > 0 && csvData[0][col]) {
                questionnaireCol = col;
                break;
            }
        }

        // Check for score columns that might indicate standardized instruments
        const scoreColumns = [];
        if (csvData.length > 0) {
            Object.keys(csvData[0]).forEach(key => {
                if (key.includes('Score') || key.includes('score') || key.includes('Total') || key.includes('total')) {
                    scoreColumns.push(key);
                }
            });
        }

        // Count questionnaires
        csvData.forEach(row => {
            let foundStandardized = false;

            // Check questionnaire column if available
            if (questionnaireCol && row[questionnaireCol]) {
                const questionnaire = row[questionnaireCol];

                // Check if this is a standardized instrument
                for (const [instrument, aliases] of Object.entries(standardizedInstruments)) {
                    if (aliases.some(alias => questionnaire.includes(alias))) {
                        standardizedCounts[instrument]++;
                        foundStandardized = true;
                        break;
                    }
                }

                // Count all questionnaires
                questionnaires[questionnaire] = (questionnaires[questionnaire] || 0) + 1;
                totalQuestionnaires++;
            }

            // Check score columns for standardized instruments
            if (!foundStandardized) {
                for (const col of scoreColumns) {
                    for (const [instrument, aliases] of Object.entries(standardizedInstruments)) {
                        if (aliases.some(alias => col.includes(alias))) {
                            if (row[col] && row[col] !== '' && !isNaN(parseFloat(row[col]))) {
                                standardizedCounts[instrument]++;

                                // Add to questionnaires count if not already counted
                                if (!questionnaires[instrument]) {
                                    questionnaires[instrument] = 0;
                                    totalQuestionnaires++;
                                }
                                questionnaires[instrument]++;

                                break;
                            }
                        }
                    }
                }
            }
        });

        // Update total count in DOM
        const totalElement = document.getElementById('totalQuestionnairesCount');
        if (totalElement) {
            totalElement.textContent = totalQuestionnaires;
        }

        // Sort questionnaires by usage
        const sortedQuestionnaires = Object.entries(questionnaires)
            .sort((a, b) => b[1] - a[1]);

        // Create chart data (limit to top 10 for better visualization)
        const topQuestionnaires = sortedQuestionnaires.slice(0, 10);
        const labels = topQuestionnaires.map(item => item[0]);
        const data = topQuestionnaires.map(item => item[1]);

        // Create chart
        ChartRegistry.createChart(
            'questionnaireCompletionChart',
            'doughnut',
            {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: getCreyosChartColors(labels.length),
                    borderWidth: 1
                }]
            },
            {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Top Questionnaires'
                    },
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const percentage = ((value / totalQuestionnaires) * 100).toFixed(1);
                                return `${context.label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        );

        // Update table
        const tableBody = document.getElementById('questionnaireCompletionTable');
        if (tableBody) {
            tableBody.innerHTML = '';

            // First add standardized instruments with non-zero counts
            const standardizedRows = [];
            for (const [instrument, count] of Object.entries(standardizedCounts)) {
                if (count > 0) {
                    const percentage = ((count / totalQuestionnaires) * 100).toFixed(1);
                    const row = document.createElement('tr');
                    row.className = 'table-primary'; // Highlight standardized instruments
                    row.innerHTML = `
                        <td><strong>${instrument}</strong> <span class="badge bg-info">Standardized</span></td>
                        <td>${count}</td>
                        <td>${percentage}%</td>
                    `;
                    standardizedRows.push(row);
                }
            }

            // Sort standardized rows by count (descending)
            standardizedRows.sort((a, b) => {
                const countA = parseInt(a.querySelector('td:nth-child(2)').textContent);
                const countB = parseInt(b.querySelector('td:nth-child(2)').textContent);
                return countB - countA;
            });

            // Add standardized rows to table
            standardizedRows.forEach(row => tableBody.appendChild(row));

            // Then add other questionnaires
            sortedQuestionnaires.forEach(([questionnaire, count]) => {
                // Skip if this is a standardized instrument we already added
                if (Object.keys(standardizedInstruments).includes(questionnaire)) {
                    return;
                }

                // Skip if this questionnaire matches any standardized instrument alias
                let isStandardized = false;
                for (const aliases of Object.values(standardizedInstruments)) {
                    if (aliases.some(alias => questionnaire.includes(alias))) {
                        isStandardized = true;
                        break;
                    }
                }
                if (isStandardized) return;

                const percentage = ((count / totalQuestionnaires) * 100).toFixed(1);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${questionnaire}</td>
                    <td>${count}</td>
                    <td>${percentage}%</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Show the section
        sectionElement.style.display = 'block';

        console.log('Questionnaire analysis complete');
    } catch (error) {
        console.error('Error in analyzeQuestionnaires:', error);
    }
}

// Analyze patient reassessments
function analyzePatientReassessments() {
    try {
        console.log('Analyzing patient reassessments...');

        // Check if the section exists in the DOM
        const sectionElement = document.getElementById('patientReassessmentSection');
        if (!sectionElement) {
            console.log('Patient reassessment section not found in DOM');
            return;
        }

        // Count assessments per patient
        const patientAssessments = {};

        csvData.forEach(row => {
            if (row['Patient ID']) {
                const patientId = row['Patient ID'];
                patientAssessments[patientId] = (patientAssessments[patientId] || 0) + 1;
            }
        });

        // Calculate reassessment metrics
        const reassessmentCounts = {
            'Once': 0,
            'Twice': 0,
            'Three Times': 0,
            'Four Times': 0,
            'Five or More': 0
        };

        Object.values(patientAssessments).forEach(count => {
            if (count === 1) reassessmentCounts['Once']++;
            else if (count === 2) reassessmentCounts['Twice']++;
            else if (count === 3) reassessmentCounts['Three Times']++;
            else if (count === 4) reassessmentCounts['Four Times']++;
            else reassessmentCounts['Five or More']++;
        });

        // Calculate total patients and patients with multiple assessments
        const totalPatients = Object.keys(patientAssessments).length;
        const patientsWithMultipleAssessments = totalPatients - reassessmentCounts['Once'];
        const percentageReassessed = totalPatients > 0
            ? ((patientsWithMultipleAssessments / totalPatients) * 100).toFixed(1)
            : 0;

        // Update DOM with reassessment metrics
        const reassessmentCountElement = document.getElementById('reassessmentCount');
        if (reassessmentCountElement) {
            reassessmentCountElement.textContent = patientsWithMultipleAssessments;
        }

        const reassessmentPercentElement = document.getElementById('reassessmentPercent');
        if (reassessmentPercentElement) {
            reassessmentPercentElement.textContent = `${percentageReassessed}%`;
        }

        // Create chart data
        const labels = Object.keys(reassessmentCounts);
        const data = Object.values(reassessmentCounts);

        // Create chart
        ChartRegistry.createChart(
            'patientReassessmentChart',
            'bar',
            {
                labels: labels,
                datasets: [{
                    label: 'Number of Patients',
                    data: data,
                    backgroundColor: `rgba(${hexToRgb(creyosColors.iconBlue)}, 0.7)`,
                    borderColor: creyosColors.iconBlue,
                    borderWidth: 1
                }]
            },
            {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Patient Assessment Frequency'
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Patients: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Patients'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Assessment Frequency'
                        }
                    }
                }
            }
        );

        // Show the section
        sectionElement.style.display = 'block';

        console.log('Patient reassessment analysis complete');
    } catch (error) {
        console.error('Error in analyzePatientReassessments:', error);
    }
}
