# CSV Analytics Dashboard Changelog

This file documents all notable changes to the CSV Analytics Dashboard project.

## [v1.9.4] - 2024-04-09
### Changed
- Updated all icons and charts to use only Creyos brand colors
- Standardized color scheme throughout the application
- Fixed version display to show correct version
- Removed sample page from the application

## [v1.9.3] - 2024-04-09
### Changed
- Removed timezone filter from the filter section
- Simplified time-based charts to use UTC time only
- Updated documentation to reflect the removal of timezone selection

## [v1.9.2] - 2024-04-09
### Fixed
- Fixed chart labels not updating when timezone filter is changed
- Simplified time labels by removing redundant timezone information
- Ensured time-based charts are properly regenerated when filters are applied

## [v1.9.1] - 2024-04-09
### Changed
- All "Select Insights for Report" options are now deselected by default
- Improved timezone display in chart titles for better readability

### Fixed
- Fixed PDF generation error related to undefined labels
- Added defensive programming to handle potential undefined variables
- Improved error handling in chart rendering

## [v1.9.0] - 2024-04-09
### Added
- Added timezone selection dropdown in the filters section
- Implemented timezone conversion for all time-based charts
- Added timezone indicators to chart titles
- Updated time labels to include timezone information

### Changed
- Time of day chart now displays times in the selected timezone
- Day of week chart now shows days based on the selected timezone
- Weekly and monthly trends now account for timezone differences
- Improved filter reset functionality to handle timezone reset

## [v1.8.3] - 2024-04-09
### Fixed
- Fixed variable scope issue in PDF generation that was causing errors
- Improved chart type handling in PDF generation

## [v1.8.2] - 2024-04-09
### Added
- Added comprehensive "How to Use" page with detailed instructions
- Added navigation links to all pages for better user experience

### Fixed
- Fixed PDF chart data error that prevented PDF generation
- Improved error handling for chart data in PDF generation

## [v1.8.1] - 2024-04-09
### Fixed
- Fixed issue with duplicate information toggle buttons
- Fixed PDF generation error related to undefined labels
- Removed jQuery dependency to comply with Content Security Policy
- Added additional error handling for chart data in PDF generation

## [v1.8.0] - 2024-04-09
### Added
- Completely redesigned PDF report layout to match Creyos branding
- Added two-column layout for pie and doughnut charts in PDF reports
- Added data tables next to pie/doughnut charts showing values and percentages
- Improved section headers with colored icons and divider lines
- Enhanced info boxes with rounded corners and icons

### Changed
- Updated header styling with improved typography and spacing
- Modified chart rendering to better utilize page space
- Improved overall PDF visual appearance and readability

## [v1.7.4] - 2024-04-09
### Improved
- Renamed "Most Used Protocols (ADHD & MCI Grouped)" to "Most Used Protocols"
- Shortened long protocol names with ellipsis for better readability
- Added tooltips to show complete protocol names on hover
- Fixed jQuery error in collapsible information boxes
- Ensured complete protocol names are shown in PDF reports

## [v1.7.3] - 2024-04-09
### Added
- Implemented Time-Based Analysis charts functionality
- Added Time of Day Distribution chart showing assessment frequency by hour
- Added Day of Week Distribution chart showing assessment frequency by day
- Added Weekly Assessment Trends chart showing assessment patterns over weeks

## [v1.7.2] - 2024-04-09
### Fixed
- Fixed jQuery dependency for collapsible information boxes
- Added jQuery to improve Bootstrap functionality
- Resolved JavaScript errors in the console

## [v1.7.1] - 2024-04-09
### Added
- Added Time-Based Analysis section with three new charts
- Added collapsible information boxes below each section
- Improved PDF report layout with visible information boxes
- Reduced logo size by 20% on first page and added padding on subsequent pages

### Removed
- Removed protocol type filter from analytics filters

## [v1.7.0] - 2024-04-09
### Changed
- Removed Advanced Analytics features
- Simplified user interface by focusing on core analytics
- Improved performance by removing unused chart components
- Streamlined report generation process

## [v1.6.5] - 2024-04-09
### Improved
- Updated charts to use real data instead of dummy data
- Made analytics filters collapsible for better UI experience
- Added logic to hide charts when no relevant data is available
- Removed duplicate information in analytics reports
- Enhanced data validation for all charts

## [v1.6.4] - 2024-04-09
### Changed
- Simplified protocol name handling to only remove timestamps
- Removed special processing rules for MCI and ADHD protocols
- Removed date display from navigation bar
- Updated version display in navigation

## [v1.6.3] - 2024-04-09
### Improved
- Optimized codebase by removing unused files and functions
- Streamlined chart creation with direct Chart.js implementation
- Reduced JavaScript file size and improved performance
- Updated footer to display "Creyos CS 2025"

## [v1.6.2] - 2024-04-09
### Changed
- Implemented direct Chart.js approach for all advanced analytics
- Simplified chart creation process
- Improved reliability of advanced analytics visualizations

## [v1.6.1] - 2024-04-09
### Fixed
- Fixed chart type issues with 'horizontalBar' in advanced analytics
- Removed unused font references
- Updated Content Security Policy to be more permissive
- Fixed protocol heatmap chart creation
- Fixed protocol comparison chart creation
- Fixed regex patterns in protocol comparison

## [v1.6.0] - 2024-04-08
### Added
- Advanced analytics features with optional inclusion in reports
- Patient Engagement analysis showing assessment distribution per patient
- Time-based analytics including time of day and day of week distributions
- Weekly usage trends with week-over-week growth calculation
- Protocol completion time analysis showing average time by protocol
- Score improvement analysis comparing first and last assessments
- Assessment Completion Filter for filtering data by completion status, date range, and protocol type
- Protocol Comparison Dashboard for side-by-side comparison of protocols
- Demographic Analysis Dashboard showing gender, age, and geographic distributions
- Advanced Analytics Dashboard with completion by time of day, protocol usage heatmap, and patient engagement timeline
- All new analytics remain optional in the "Select Insights for Report" section

## [v1.5.9] - 2024-04-08
### Changed
- Updated Assessment Completion Rate calculation
- Added special handling for completion ratios like 1/1, 2/1, 3/1, 4/1
- Added special handling for completion ratios like 3/2, 4/3, 5/4
- Improved accuracy of completion statistics
- Enhanced chart visualization with more accurate data

## [v1.5.8] - 2024-04-08
### Changed
- Enhanced protocol detection with completion ratio analysis
- Added detection for protocols with ratios like "3/2" or "4/2"
- Implemented mathematical comparison to identify ADHD and MCI protocols
- Improved handling of protocols with and without SAR keyword
- Refined protocol categorization logic with more precise rules

## [v1.5.7] - 2024-04-08
### Changed
- Improved protocol handling for entries without timestamps
- Enhanced protocol categorization logic to distinguish between protocols with and without timestamps
- For protocols without timestamps, the entire field is now used as is (including commas)
- Clarified that ADHD and MCI protocols always contain timestamps

## [v1.5.6] - 2024-04-08
### Added
- MCI (Mild Cognitive Impairment) detection based on Creyos Dementia Assessment tasks
- Special handling for protocols containing IADL and IQCODE
- Automatic categorization of protocols with 3+ MCI-related tasks

### Changed
- Updated protocol chart title to "Most Used Protocols (ADHD & MCI Grouped)"
- Added special color highlighting for MCI protocols
- Enhanced protocol categorization logic with multiple special cases

## [v1.5.5] - 2024-04-08
### Changed
- Refined protocol counting to handle ADHD as a special case
- Updated protocol analysis to keep full protocol names (with commas) for non-ADHD protocols
- Improved chart coloring with special highlighting for ADHD protocols
- Updated chart title to better reflect the grouping approach

## [v1.5.4] - 2024-04-08
### Changed
- Improved protocol categorization with intelligent grouping
- Enhanced protocol analysis to ignore timestamps
- Added special handling for ADHD protocols containing "SAR"
- Updated protocol chart with category-based colors
- Added percentage information to protocol chart tooltips

## [v1.5.3] - 2024-04-08
### Fixed
- Content Security Policy (CSP) issues with external fonts
- Data preview modal now works correctly in all environments
- PDF generation errors with text rendering
- Improved error handling throughout the application

### Changed
- Switched from Proxima Nova to system fonts for better compatibility
- Enhanced modal handling with Bootstrap API integration
- Improved async/await handling in PDF generation

## [v1.5.2] - 2024-04-08
### Added
- Data preview modal showing first 15 rows of imported data
- Click-anywhere-to-close functionality for the preview modal

### Fixed
- PDF logo aspect ratio is now maintained properly
- Images in PDF reports no longer appear stretched
- Improved PDF generation with proper image handling

## [v1.5.1] - 2024-04-08
### Changed
- Updated UI with Proxima Nova font throughout the application
- Implemented Creyos brand color palette
- Enhanced visual consistency with Creyos brand guidelines
- Improved typography and spacing for better readability
- Updated button and form styles to match Creyos design system

## [v1.5.0] - 2024-04-08
### Added
- CSV to JSON conversion for improved data processing
- Automatic type conversion for numeric values
- Date parsing and formatting for date fields
- Enhanced data handling with proper data types
- Fallback mechanism to ensure backward compatibility

## [v1.4.2] - 2024-04-08
### Fixed
- Version tooltip now displays the current date instead of static date
- Improved version display consistency across the application

## [v1.4.1] - 2024-04-08
### Improved
- Enhanced How to Use page with modern card-based layout
- Replaced placeholder images with Bootstrap icons
- Added Key Features section highlighting main benefits
- Improved overall user experience with better visual hierarchy
- Optimized page layout following Bootstrap design guidelines

## [v1.4.0] - 2024-04-08
### Changed
- Reorganized file structure with proper image organization
- Moved all image assets to the images folder
- Updated all image references to use the new paths
- Removed unused files to reduce clutter
- Improved overall project organization

## [v1.3.9] - 2024-04-08
### Improved
- Enhanced CSV parsing with support for quoted fields containing commas
- Added UTF-8 encoding support for international character sets
- Improved CSV import configuration to match standard import settings
- Enhanced data handling for complex CSV files

## [v1.3.8] - 2024-04-08
### Fixed
- Fixed PDF filename generation error
- Resolved variable scope issue in PDF generation
- Improved error handling in PDF export

## [v1.3.7] - 2024-04-08
### Added
- Added Creyos logo to PDF headers on all pages
- Enhanced pie chart labels to show both values and percentages
- Added small logo and title to subsequent PDF pages
- Improved overall PDF layout and branding

## [v1.3.6] - 2024-04-08
### Fixed
- Fixed PDF generation with manual chart labels
- Resolved "Maximum call stack size exceeded" error
- Improved chart rendering in PDFs with direct canvas manipulation
- Enhanced data label visibility with better positioning
- Ensured proper display of values on all chart types

## [v1.3.5] - 2024-04-08
### Fixed
- Fixed missing charts in PDF exports
- Changed logo in report preview to CreyosLogo.png
- Improved PDF margins for better layout
- Enhanced chart rendering with data labels
- Simplified PDF generation process

## [v1.3.4] - 2024-04-08
### Fixed
- Fixed PDF generation to properly display chart values
- Resolved maximum call stack size exceeded error
- Improved chart rendering in PDFs with data labels
- Enhanced PDF generation reliability

## [v1.3.3] - 2024-04-08
### Changed
- Removed version header from PDF exports
- Enhanced chart data labels for better visibility in PDFs
- Improved label styling with background colors for better contrast
- Increased font size for better readability

## [v1.3.2] - 2024-04-08
### Added
- Added data labels to charts in PDF exports
- Integrated Chart.js DataLabels plugin for better data visualization
- Enhanced chart readability in exported PDFs
- Improved value display for different chart types

## [v1.3.1] - 2024-04-08
### Fixed
- Fixed Content Security Policy (CSP) issues with Google Fonts
- Resolved chart rendering errors by properly defining variables
- Switched to system fonts for better compatibility
- Updated script references to use the correct version numbers
- Changed logo to use CreyosLogo-white.png for better visibility

## [v1.3.0] - 2024-04-08
### Added
- Modern color palette with vibrant, contemporary colors
- Enhanced chart styling with modern colors and better readability
- Improved card and container styling with subtle shadows and animations
- Better visual hierarchy throughout the application

### Changed
- Removed dark theme in favor of a single, refined modern theme
- Updated chart rendering to use the new color palette
- Improved overall UI/UX with modern design principles
- Enhanced accessibility with better contrast and focus states

## [v1.2.0] - 2024-04-08
### Added
- Theme system with CSS variables for consistent styling
- Theme-aware charts that update colors based on theme
- Improved accessibility with better focus states and contrast ratios
- Smooth transitions for UI elements

### Changed
- Updated chart rendering to be theme-aware
- Improved overall UI/UX with modern design principles
- Enhanced visual hierarchy and consistency

## [v1.1.2] - 2024-04-08
### Fixed
- PDF generation now correctly includes all charts and data
- Improved loading indicator for PDF generation
- Enhanced error handling during PDF creation
- Fixed multi-page PDF handling

## [v1.1.1] - 2024-04-08
### Added
- Cache busting mechanism to ensure latest scripts are loaded
- Clear cache page with instructions for different browsers
- Cache clearing link in navigation bar

### Fixed
- Issues with browser caching old script versions
- Updated script loading to include version parameters

## [v1.1.0] - 2024-04-08
### Added
- Complete rewrite of chart system with ChartRegistry
- Centralized chart management for better reliability
- Enhanced error handling for chart creation
- Improved logging throughout chart system

### Changed
- Separated chart functionality into dedicated file
- Improved code organization and maintainability
- Enhanced chart initialization process

## [v1.0.2] - 2024-04-08
### Fixed
- Chart initialization issues
- Canvas creation and management
- Error handling for chart rendering
- Chart element lookup and identification

## [v1.0.1] - 2024-04-08
### Added
- Version display in navigation bar
- Version history tracking in version.js
- Automatic version display updates
- Build number and date tracking

## [v1.0.0] - 2024-04-08
### Added
- Initial release of CSV Analytics Dashboard
- CSV file parsing and data analysis
- Interactive data visualization with Chart.js
- Report generation with insights
- PDF export functionality
- Bootstrap-based responsive design
- Creyos branding and logo integration

## Pre-release Changes

### UI Improvements
- Added Creyos logo to navigation bar and reports
- Removed copyright text "© 2023 CSV Analytics Dashboard"
- Improved layout for better visibility
- Enhanced responsive design for mobile devices

### Bug Fixes
- Fixed placeholder image loading issues
- Resolved script loading errors
- Improved error handling throughout application
- Fixed Content Security Policy issues

### Security Enhancements
- Added Content Security Policy headers
- Implemented script blocker for unwanted tracking scripts
- Allowed necessary CDN resources while blocking tracking
- Enhanced overall application security

## Development Notes

The CSV Analytics Dashboard was developed to provide data visualization for CSV files with insights including:
- Patient counts and demographics
- Assessment types and methods
- Completion times and rates
- Protocol usage statistics
- Practitioner activity metrics

The application features Bootstrap styling, Chart.js visualizations, and PDF export functionality. It includes both light and dark themes for better user experience and accessibility.
