# CSV Analytics Dashboard

**Current Version:** v1.2.0

A powerful tool for analyzing and generating reports from Creyos aggregate report CSV files.

## Features

- Upload and analyze CSV data from Creyos aggregate reports
- Extract key insights including:
  - Unique patients count
  - Assessment methods distribution (onsite, offsite, copy link)
  - Average completion time
  - Most used protocols
  - Practitioner activity
  - Patient age distribution
  - Assessment completion rate
  - Monthly assessment trends
  - Score distribution
- Select which insights to include in your report
- Generate downloadable PDF reports
- Interactive charts and visualizations
- Real-time preview of the report
- Light and dark theme support
- Responsive design for all device sizes

## How to Use

1. Open `index.html` in your web browser
2. Upload your aggregate report CSV file
3. Select the insights you want to include in your report
4. Preview the report with interactive charts
5. Generate and download a PDF report

## Theme Selection

The dashboard supports both light and dark themes:

- Click the sun/moon icon in the navigation bar to toggle between themes
- Your theme preference will be saved for future sessions
- The dashboard will automatically detect your system theme preference on first visit

## Recent Changes

### v1.2.0
- Added dark/light theme system
- Improved UI/UX with modern design principles
- Enhanced accessibility features

### v1.1.2
- Fixed PDF generation
- Improved loading indicators

### v1.1.1
- Added cache busting mechanism
- Improved script loading

For a complete list of changes, see the CHANGELOG.md file.

## Technical Details

This application uses:
- Bootstrap 5 for styling
- Chart.js for data visualization
- PapaParse for CSV parsing
- jsPDF and html2canvas for PDF generation

## CSV Format

The application expects a CSV file with the following columns:
- Patient ID
- Administration Method
- Session Start
- Session Completed
- Protocol Name
- Provider/Practitioner
- Date of Birth
- Time administered at
- Assessments Completed
- Various score columns (for cognitive tests)

## Browser Compatibility

This application works best in modern browsers:
- Chrome
- Firefox
- Edge
- Safari

## Privacy

All data processing happens locally in your browser. No data is sent to any server.
