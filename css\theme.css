/* Creyos Theme for CSV Analytics Dashboard */

/* Using system fonts instead of Proxima Nova due to CSP restrictions */

/* Theme Variables - Using Bootstrap 5 CSS Variables */
:root {
    /* Creyos Core Brand Colors */
    --creyos-dark-blue: #3A4661; /* HEX: 3A4661, RGB: 58, 70, 97 */
    --creyos-accessible-teal: #0E7A95; /* HEX: 0E7A95, RGB: 14, 122, 149 */
    --creyos-teal: #4393A7; /* HEX: 4393A7, RGB: 67, 147, 167 */
    --creyos-icon-blue: #12C0DB; /* HEX: 12C0DB, RGB: 18, 192, 219 */

    /* Creyos Brand Highlight Colors */
    --creyos-accent-blue: #BDF2FF; /* HEX: BDF2FF, RGB: 189, 242, 255 */
    --creyos-accent-blue-80: #CAF5FF; /* HEX: CAF5FF, RGB: 202, 245, 255 */
    --creyos-accent-blue-60: #D7F7FF; /* HEX: D7F7FF, RGB: 215, 247, 255 */
    --creyos-accent-blue-40: #E5FAFF; /* HEX: E5FAFF, RGB: 229, 250, 255 */

    /* Override Bootstrap Variables with Creyos Theme */
    --bs-primary: var(--creyos-icon-blue);
    --bs-secondary: var(--creyos-teal);
    --bs-success: var(--creyos-accessible-teal);
    --bs-info: var(--creyos-accent-blue);
    --bs-warning: var(--creyos-teal);
    --bs-danger: var(--creyos-dark-blue);
    --bs-light: var(--creyos-accent-blue-40);
    --bs-dark: var(--creyos-dark-blue);

    /* Background and Text Colors */
    --bs-body-bg: #ffffff;
    --bs-body-bg-rgb: 255, 255, 255;
    --bs-body-color: var(--creyos-dark-blue);
    --bs-body-color-rgb: 58, 70, 97;

    /* Font Family */
    --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

    /* Border and Shadow */
    --bs-border-color: #dee2e6;
    --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
    --bs-card-cap-bg: #f8f9fa;

    /* Chart Colors - Creyos Theme */
    --chart-color-1: var(--creyos-icon-blue);
    --chart-color-2: var(--creyos-teal);
    --chart-color-3: var(--creyos-accessible-teal);
    --chart-color-4: var(--creyos-dark-blue);
    --chart-color-5: var(--creyos-accent-blue);
    --chart-color-6: var(--creyos-accent-blue-80);
    --chart-color-7: var(--creyos-accent-blue-60);
    --chart-color-8: var(--creyos-accent-blue-40);

    /* UI Elements */
    --navbar-bg: linear-gradient(90deg, var(--creyos-icon-blue), var(--creyos-accessible-teal));
    --navbar-text: #ffffff;
    --navbar-active: #ffffff;
    --navbar-hover: rgba(255, 255, 255, 0.8);
    --card-shadow: 0 0.5rem 1rem rgba(58, 70, 97, 0.1);
    --input-focus-shadow: 0 0 0 0.25rem rgba(18, 192, 219, 0.25);
}

    /* UI Elements */
    --navbar-bg: linear-gradient(90deg, var(--creyos-icon-blue), var(--creyos-accessible-teal));
    --navbar-text: #ffffff;
    --navbar-active: #ffffff;
    --navbar-hover: rgba(255, 255, 255, 0.8);
    --card-shadow: 0 0.5rem 1rem rgba(58, 70, 97, 0.1);
    --input-focus-shadow: 0 0 0 0.25rem rgba(18, 192, 219, 0.25);
}

/* Base Theme Styles */
body {
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    font-weight: 400;
}

/* Navbar Styles */
.navbar {
    background: var(--navbar-bg) !important;
    box-shadow: 0 2px 10px rgba(14, 122, 149, 0.2);
}

.navbar-brand, .nav-link {
    color: var(--navbar-text) !important;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
}

.navbar-brand {
    font-weight: 600;
}

.nav-link:hover {
    color: var(--navbar-hover) !important;
}

.nav-link.active {
    color: var(--navbar-active) !important;
    font-weight: 600;
    position: relative;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--navbar-active);
    border-radius: 2px;
}

/* Card and Container Styles */
.card, .jumbotron, .report-preview {
    background-color: var(--bs-body-bg);
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 1.5rem rgba(18, 192, 219, 0.1);
}

.card-header {
    background-color: var(--creyos-accent-blue-40);
    border-bottom-color: var(--bs-border-color);
    color: var(--creyos-dark-blue);
    font-weight: 600;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Chart Containers */
[id$="Chart"] {
    background-color: var(--bs-body-bg);
    border: 1px solid var(--creyos-accent-blue);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    padding: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

[id$="Chart"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(18, 192, 219, 0.15);
}

/* Report Styles */
.report-preview {
    background-color: var(--bs-body-bg);
    padding: 2rem;
    border: 1px solid var(--creyos-accent-blue-60);
}

.report-section {
    margin-bottom: 2rem;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.report-section h4 {
    color: var(--creyos-dark-blue);
    font-weight: 600;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--creyos-icon-blue);
}

/* Button Styles */
.btn-primary {
    background-color: var(--creyos-icon-blue);
    border-color: var(--creyos-icon-blue);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(18, 192, 219, 0.2);
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--creyos-accessible-teal);
    border-color: var(--creyos-accessible-teal);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(18, 192, 219, 0.3);
    color: #ffffff;
}

.btn-secondary {
    background-color: var(--creyos-teal);
    border-color: var(--creyos-teal);
    color: #ffffff;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: var(--creyos-dark-blue);
    border-color: var(--creyos-dark-blue);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(58, 70, 97, 0.3);
}

/* Success, Warning, Danger, Info Button Styles */
.btn-success {
    background-color: var(--creyos-accessible-teal);
    border-color: var(--creyos-accessible-teal);
    color: #ffffff;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(14, 122, 149, 0.2);
}

.btn-success:hover, .btn-success:focus {
    background-color: var(--creyos-teal);
    border-color: var(--creyos-teal);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(14, 122, 149, 0.3);
}

.btn-warning {
    background-color: var(--creyos-teal);
    border-color: var(--creyos-teal);
    color: #ffffff;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(67, 147, 167, 0.2);
}

.btn-warning:hover, .btn-warning:focus {
    background-color: var(--creyos-accessible-teal);
    border-color: var(--creyos-accessible-teal);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(67, 147, 167, 0.3);
}

.btn-danger {
    background-color: var(--creyos-dark-blue);
    border-color: var(--creyos-dark-blue);
    color: #ffffff;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(58, 70, 97, 0.2);
}

.btn-danger:hover, .btn-danger:focus {
    background-color: var(--creyos-accessible-teal);
    border-color: var(--creyos-accessible-teal);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(58, 70, 97, 0.3);
}

.btn-info {
    background-color: var(--creyos-accent-blue);
    border-color: var(--creyos-accent-blue);
    color: var(--creyos-dark-blue);
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(189, 242, 255, 0.4);
}

.btn-info:hover, .btn-info:focus {
    background-color: var(--creyos-accent-blue-80);
    border-color: var(--creyos-accent-blue-80);
    color: var(--creyos-dark-blue);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(189, 242, 255, 0.5);
}

/* Table Styles */
.table {
    color: var(--creyos-dark-blue);
    border-color: var(--creyos-accent-blue-60);
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.table thead th {
    border-bottom-color: var(--creyos-accent-blue);
    background-color: var(--creyos-accent-blue-40);
    color: var(--creyos-dark-blue);
    font-weight: 600;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--creyos-accent-blue-40);
}

/* Form Styles */
.form-control, .form-select {
    background-color: var(--bs-body-bg);
    color: var(--creyos-dark-blue);
    border-color: var(--creyos-accent-blue-60);
    border-radius: 0.375rem;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.form-control:focus, .form-select:focus {
    background-color: var(--bs-body-bg);
    color: var(--creyos-dark-blue);
    border-color: var(--creyos-icon-blue);
    box-shadow: var(--input-focus-shadow);
}

.form-label {
    color: var(--creyos-dark-blue);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Text Colors */
.text-primary {
    color: var(--creyos-icon-blue) !important;
}

.text-secondary {
    color: var(--creyos-teal) !important;
}

.text-success {
    color: var(--creyos-accessible-teal) !important;
}

.text-info {
    color: var(--creyos-accent-blue) !important;
}

.text-warning {
    color: var(--creyos-teal) !important;
}

.text-danger {
    color: var(--creyos-dark-blue) !important;
}

/* Creyos Brand Color Classes for Icons */
.text-creyos-icon-blue {
    color: var(--creyos-icon-blue) !important;
}

.text-creyos-teal {
    color: var(--creyos-teal) !important;
}

.text-creyos-accessible-teal {
    color: var(--creyos-accessible-teal) !important;
}

.text-creyos-dark-blue {
    color: var(--creyos-dark-blue) !important;
}

.text-creyos-accent-blue {
    color: var(--creyos-accent-blue) !important;
}

/* Additional Creyos specific text colors can be added here */

/* Remove non-brand colors */

.text-muted {
    color: var(--creyos-teal) !important;
    opacity: 0.75;
}

/* Version Display */
.version-display {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    transition: all 0.2s ease-in-out;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    cursor: help;
}

.version-display:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Accessibility Improvements */
:focus {
    outline: 2px solid var(--creyos-icon-blue);
    outline-offset: 2px;
}

.btn:focus, .form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(18, 192, 219, 0.25);
}

/* Alert Styles */
.alert-primary {
    background-color: rgba(18, 192, 219, 0.1);
    border-color: var(--creyos-icon-blue);
    color: var(--creyos-dark-blue);
}

.alert-secondary {
    background-color: rgba(67, 147, 167, 0.1);
    border-color: var(--creyos-teal);
    color: var(--creyos-dark-blue);
}

.alert-success {
    background-color: rgba(14, 122, 149, 0.1);
    border-color: var(--creyos-accessible-teal);
    color: var(--creyos-dark-blue);
}

.alert-info {
    background-color: rgba(189, 242, 255, 0.2);
    border-color: var(--creyos-accent-blue);
    color: var(--creyos-dark-blue);
}

.alert-warning {
    background-color: rgba(67, 147, 167, 0.1);
    border-color: var(--creyos-teal);
    color: var(--creyos-dark-blue);
}

.alert-danger {
    background-color: rgba(58, 70, 97, 0.1);
    border-color: var(--creyos-dark-blue);
    color: var(--creyos-dark-blue);
}

/* Badge Styles */
.badge-primary {
    background-color: var(--creyos-icon-blue);
    color: #ffffff;
}

.badge-secondary {
    background-color: var(--creyos-teal);
    color: #ffffff;
}

.badge-success {
    background-color: var(--creyos-accessible-teal);
    color: #ffffff;
}

.badge-info {
    background-color: var(--creyos-accent-blue);
    color: var(--creyos-dark-blue);
}

.badge-warning {
    background-color: var(--creyos-teal);
    color: #ffffff;
}

.badge-danger {
    background-color: var(--creyos-dark-blue);
    color: #ffffff;
}

/* Animation Effects */
.btn, .nav-link, .card, .form-control {
    transition: all 0.2s ease-in-out;
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}



/* Print Styles */
@media print {
    body {
        background-color: white;
        color: var(--creyos-dark-blue);
        font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    .report-preview {
        background-color: white;
        box-shadow: none;
        border: none;
    }

    [id$="Chart"] {
        background-color: white;
        box-shadow: none;
        border: 1px solid var(--creyos-accent-blue-60);
    }

    h1, h2, h3, h4, h5, h6 {
        color: var(--creyos-dark-blue);
        font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-weight: 600;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .report-preview {
        padding: 1rem;
    }

    [id$="Chart"] {
        height: 250px !important;
    }

    .version-display {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    h1, h2, h3 {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1rem;
    }

}
